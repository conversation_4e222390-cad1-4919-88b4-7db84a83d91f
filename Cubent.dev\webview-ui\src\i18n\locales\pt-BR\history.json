{"recentTasks": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON>er to<PERSON>", "tokens": "Tokens: ↑{{in}} ↓{{out}}", "cache": "Cache: +{{writes}} → {{reads}}", "apiCost": "Custo da API: ${{cost}}", "history": "Hist<PERSON><PERSON><PERSON>", "exitSelectionMode": "Sair do modo de seleção", "enterSelectionMode": "Entrar no modo de seleção", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "Pesquisar no histórico...", "newest": "<PERSON><PERSON> recentes", "oldest": "<PERSON><PERSON> antigas", "mostExpensive": "<PERSON><PERSON> caras", "mostTokens": "Mais tokens", "mostRelevant": "<PERSON><PERSON>", "deleteTaskTitle": "Excluir tarefa (Shift + Clique para pular confirmação)", "tokensLabel": "Tokens:", "cacheLabel": "Cache:", "apiCostLabel": "Custo da API:", "copyPrompt": "<PERSON><PERSON><PERSON> prompt", "exportTask": "Exportar tarefa", "deleteTask": "Excluir tarefa", "deleteTaskMessage": "Tem certeza que deseja excluir esta tarefa? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "exitSelection": "<PERSON><PERSON> <PERSON>", "selectionMode": "Modo de se<PERSON>ção", "deselectAll": "<PERSON><PERSON><PERSON> tudo", "selectAll": "Selecionar tudo", "selectedItems": "{{selected}}/{{total}} itens selecionados", "clearSelection": "<PERSON><PERSON>", "deleteSelected": "Excluir selecionados", "deleteTasks": "Excluir tarefas", "confirmDeleteTasks": "Tem certeza que deseja excluir {{count}} tarefas?", "deleteTasksWarning": "As tarefas excluídas não podem ser recuperadas. Por favor, certifique-se de que deseja prosseguir.", "deleteItems": "Excluir {{count}} itens", "showAllWorkspaces": "Mostrar tarefas de todos os espaços de trabalho"}