module.exports = {

"[project]/apps/api/app/icon.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/icon.99eff9f1.png");}}),
"[project]/apps/api/app/icon.png.mjs { IMAGE => \"[project]/apps/api/app/icon.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$api$2f$app$2f$icon$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/apps/api/app/icon.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$api$2f$app$2f$icon$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 32,
    height: 32
};
}}),

};

//# sourceMappingURL=apps_api_app_f91e11fb._.js.map