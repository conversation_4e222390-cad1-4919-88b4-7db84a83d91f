{"extension": {"name": "cubent Code", "description": "您編輯器中的完整 AI 開發團隊。"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "歡迎，{{name}}！您有 {{count}} 條通知。", "items": {"zero": "沒有項目", "one": "1 個項目", "other": "{{count}}個項目"}, "confirmation": {"reset_state": "您確定要重設擴充套件中的所有狀態和金鑰儲存嗎？此操作無法復原。", "delete_config_profile": "您確定要刪除此設定檔案嗎？", "delete_custom_mode": "您確定要刪除此自訂模式嗎？", "delete_message": "您想刪除哪些內容？", "just_this_message": "僅這則訊息", "this_and_subsequent": "這則訊息及所有後續訊息"}, "errors": {"invalid_mcp_config": "專案 MCP 設定格式無效", "invalid_mcp_settings_format": "MCP 設定 JSON 格式無效。請確保您的設定遵循正確的 JSON 格式。", "invalid_mcp_settings_syntax": "MCP 設定 JSON 格式無效。請檢查您的設定檔案是否有語法錯誤。", "invalid_mcp_settings_validation": "MCP 設定格式無效：{{errorMessages}}", "failed_initialize_project_mcp": "初始化專案 MCP 伺服器失敗：{{error}}", "invalid_data_uri": "資料 URI 格式無效", "checkpoint_timeout": "嘗試恢復檢查點時超時。", "checkpoint_failed": "恢復檢查點失敗。", "no_workspace": "請先開啟專案資料夾", "update_support_prompt": "更新支援訊息失敗", "reset_support_prompt": "重設支援訊息失敗", "enhance_prompt": "增強訊息失敗", "get_system_prompt": "取得系統訊息失敗", "search_commits": "搜尋提交失敗", "save_api_config": "儲存 API 設定失敗", "create_api_config": "建立 API 設定失敗", "rename_api_config": "重新命名 API 設定失敗", "load_api_config": "載入 API 設定失敗", "delete_api_config": "刪除 API 設定失敗", "list_api_config": "取得 API 設定列表失敗", "update_server_timeout": "更新伺服器超時設定失敗", "failed_update_project_mcp": "更新專案 MCP 伺服器失敗", "create_mcp_json": "建立或開啟 .cubent/mcp.json 失敗：{{error}}", "hmr_not_running": "本機開發伺服器沒有執行，HMR 將不起作用。請在啟動擴充套件前執行'npm run dev'以啟用 HMR。", "retrieve_current_mode": "從狀態中檢索目前模式失敗。", "failed_delete_repo": "刪除關聯的影子倉庫或分支失敗：{{error}}", "failed_remove_directory": "刪除工作目錄失敗：{{error}}", "custom_storage_path_unusable": "自訂儲存路徑 \"{{path}}\" 無法使用，將使用預設路徑", "cannot_access_path": "無法存取路徑 {{path}}：{{error}}", "settings_import_failed": "設定匯入失敗：{{error}}。", "mistake_limit_guidance": "這可能表明模型思維過程失敗或無法正確使用工具，可透過使用者指導來緩解（例如「嘗試將工作分解為更小的步驟」）。", "violated_organization_allowlist": "執行工作失敗：目前設定檔違反了您的組織設定", "condense_failed": "壓縮上下文失敗", "condense_not_enough_messages": "沒有足夠的訊息來壓縮上下文", "condensed_recently": "上下文最近已壓縮；跳過此次嘗試", "condense_handler_invalid": "壓縮上下文的 API 處理程式無效", "condense_context_grew": "壓縮過程中上下文大小增加；跳過此次嘗試"}, "warnings": {"no_terminal_content": "沒有選擇終端機內容", "missing_task_files": "此工作的檔案遺失。您想從工作列表中刪除它嗎？"}, "info": {"no_changes": "沒有找到更改。", "clipboard_copy": "系統訊息已成功複製到剪貼簿", "history_cleanup": "已從歷史記錄中清理{{count}}個缺少檔案的工作。", "mcp_server_restarting": "正在重啟{{serverName}}MCP 伺服器...", "mcp_server_connected": "{{serverName}}MCP 伺服器已連接", "mcp_server_deleted": "已刪除 MCP 伺服器：{{serverName}}", "mcp_server_not_found": "在設定中沒有找到伺服器\"{{serverName}}\"", "custom_storage_path_set": "自訂儲存路徑已設定：{{path}}", "default_storage_path": "已恢復使用預設儲存路徑", "settings_imported": "設定已成功匯入。"}, "answers": {"yes": "是", "no": "否", "cancel": "取消", "remove": "刪除", "keep": "保留"}, "tasks": {"canceled": "工作錯誤：它已被使用者停止並取消。", "deleted": "工作失敗：它已被使用者停止並刪除。"}, "storage": {"prompt_custom_path": "輸入自訂會話歷史儲存路徑，留空以使用預設位置", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "請輸入絕對路徑（例如 D:\\RooCodeStorage 或 /home/<USER>/storage）", "enter_valid_path": "請輸入有效的路徑"}, "input": {"task_prompt": "讓 cubent 做什麼？", "task_placeholder": "在這裡輸入工作"}, "settings": {"providers": {"groqApiKey": "Groq API 金鑰", "getGroqApiKey": "取得 Groq API 金鑰"}}}