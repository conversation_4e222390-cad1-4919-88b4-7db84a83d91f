{"greeting": "Bienvenido a cubent Code", "chat": {"title": "Cha<PERSON>", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Costo de API:", "contextWindow": "Longitud del contexto:", "closeAndStart": "Cerrar chat e iniciar uno nuevo", "export": "Exportar historial de chat", "delete": "Eliminar chat (Shift + Clic para omitir confirmación)", "condenseContext": "Condensar contexto de forma inteligente"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON>", "retry": {"title": "Reintentar", "tooltip": "Intenta la operación de nuevo"}, "startNewChat": {"title": "Iniciar nuevo chat", "tooltip": "Comienza un nuevo chat"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de todos modos", "tooltip": "Continuar mientras se ejecuta el comando"}, "save": {"title": "Guardar", "tooltip": "Guardar los cambios del archivo"}, "tokenProgress": {"availableSpace": "Espacio disponible: {{amount}} tokens", "tokensUsed": "Tokens utilizados: {{used}} de {{total}}", "reservedForResponse": "Reservado para respuesta del modelo: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Re<PERSON>zar esta acción"}, "completeSubtaskAndReturn": "Completar subtarea y regresar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprobar esta acción"}, "runCommand": {"title": "Ejecutar comando", "tooltip": "Ejecutar este comando"}, "proceedWhileRunning": {"title": "Continuar mientras se ejecuta", "tooltip": "Continuar a pesar de las advertencias"}, "killCommand": {"title": "Terminar comando", "tooltip": "Terminar el comando actual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> la tarea actual"}, "terminate": {"title": "Terminar", "tooltip": "Terminar la tarea actual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar la operación actual"}, "scrollToBottom": "Desplazarse al final del chat", "about": "Genera, refactoriza y depura código con asistencia de IA. Consulta nuestra <DocsLink>documentación</DocsLink> para obtener más información.", "onboarding": "<strong>Tu lista de tareas en este espacio de trabajo está vacía.</strong> Comienza escribiendo una tarea abajo. ¿No estás seguro cómo empezar? Lee más sobre lo que cubent puede hacer por ti en <DocsLink>la documentación</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON><PERSON>", "description": "Divide las tareas en partes más pequeñas y manejables."}, "stickyModels": {"title": "Modos persistentes", "description": "Cada modo recuerda tu último modelo utilizado"}, "tools": {"title": "Herramientas", "description": "Permite que la IA resuelva problemas navegando por la web, ejecutando comandos y mucho más."}, "customizableModes": {"title": "Modos personalizables", "description": "Personalidades especializadas con sus propios comportamientos y modelos asignados"}}, "selectMode": "Seleccionar modo de interacción", "selectApiConfig": "Seleccionar configuración de API", "enhancePrompt": "Mejorar el mensaje con contexto adicional", "addImages": "Agregar imágenes al mensaje", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "typeMessage": "Escribe un mensaje...", "typeTask": "Escribe tu tarea aquí...", "addContext": "@ para agregar contexto, / para cambiar modos", "dragFiles": "mantén shift para arrastrar archivos", "dragFilesImages": "mantén shift para arrastrar archivos/imágenes", "enhancePromptDescription": "El botón 'Mejorar el mensaje' ayuda a mejorar tu petición proporcionando contexto adicional, aclaraciones o reformulaciones. Intenta escribir una petición aquí y haz clic en el botón nuevamente para ver cómo funciona.", "errorReadingFile": "Error al leer el archivo:", "noValidImages": "No se procesaron imágenes válidas", "separator": "Separador", "edit": "Editar...", "forNextMode": "para el siguiente modo", "error": "Error", "diffError": {"title": "Edición fallida"}, "troubleMessage": "cubent está teniendo problemas...", "apiRequest": {"title": "Solicitud API", "failed": "Solicitud API falló", "streaming": "Solicitud API...", "cancelled": "Solicitud API cancelada", "streamingFailed": "Transmisión API falló"}, "checkpoint": {"initial": "Punto de control inicial", "regular": "Punto de control", "initializingWarning": "Todavía inicializando el punto de control... Si esto tarda demasiado, puedes desactivar los puntos de control en la <settingsLink>configuración</settingsLink> y reiniciar tu tarea.", "menu": {"viewDiff": "Ver diferencias", "restore": "Restaurar punto de control", "restoreFiles": "Restaurar archivos", "restoreFilesDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto.", "restoreFilesAndTask": "Restaurar archivos y tarea", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta acción no se puede deshacer.", "restoreFilesAndTaskDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto y elimina todos los mensajes posteriores a este punto."}, "current": "Actual"}, "instructions": {"wantsToFetch": "cubent quiere obtener instrucciones detalladas para ayudar con la tarea actual"}, "fileOperations": {"wantsToRead": "cubent quiere leer este archivo:", "wantsToReadOutsideWorkspace": "cubent quiere leer este archivo fuera del espacio de trabajo:", "didRead": "cubent leyó este archivo:", "wantsToEdit": "cubent quiere editar este archivo:", "wantsToEditOutsideWorkspace": "cubent quiere editar este archivo fuera del espacio de trabajo:", "wantsToCreate": "cubent quiere crear un nuevo archivo:", "wantsToSearchReplace": "cubent quiere realizar búsqueda y reemplazo en este archivo:", "didSearchReplace": "cubent realizó búsqueda y reemplazo en este archivo:", "wantsToInsert": "cubent quiere insertar contenido en este archivo:", "wantsToInsertWithLineNumber": "cubent quiere insertar contenido en este archivo en la línea {{lineNumber}}:", "wantsToInsertAtEnd": "cubent quiere añadir contenido al final de este archivo:", "wantsToReadAndXMore": "cubent quiere leer este archivo y {{count}} más:", "wantsToReadMultiple": "cubent quiere leer varios archivos:"}, "directoryOperations": {"wantsToViewTopLevel": "cubent quiere ver los archivos de nivel superior en este directorio:", "didViewTopLevel": "cubent vio los archivos de nivel superior en este directorio:", "wantsToViewRecursive": "cubent quiere ver recursivamente todos los archivos en este directorio:", "didViewRecursive": "cubent vio recursivamente todos los archivos en este directorio:", "wantsToViewDefinitions": "cubent quiere ver nombres de definiciones de código fuente utilizados en este directorio:", "didViewDefinitions": "cubent vio nombres de definiciones de código fuente utilizados en este directorio:", "wantsToSearch": "cubent quiere buscar en este directorio <code>{{regex}}</code>:", "didSearch": "cubent buscó en este directorio <code>{{regex}}</code>:"}, "commandOutput": "Salida del comando", "response": "Respuesta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "cubent quiere usar una herramienta en el servidor MCP {{serverName}}:", "wantsToAccessResource": "cubent quiere acceder a un recurso en el servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "cubent quiere cambiar a modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "cubent quiere cambiar a modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "cubent cambió a modo <code>{{mode}}</code>", "didSwitchWithReason": "cubent cambió a modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "cubent quiere crear una nueva subtarea en modo <code>{{mode}}</code>:", "wantsToFinish": "cubent quiere finalizar esta subtarea", "newTaskContent": "Instrucciones de la subtarea", "completionContent": "Subtarea completada", "resultContent": "Resultados de la subtarea", "defaultResult": "Por favor, continúa con la siguiente tarea.", "completionInstructions": "¡Subtarea completada! Puedes revisar los resultados y sugerir correcciones o próximos pasos. Si todo se ve bien, confirma para devolver el resultado a la tarea principal."}, "questions": {"hasQuestion": "Pendiente de aclaración:"}, "taskCompleted": "Resul<PERSON><PERSON>", "powershell": {"issues": "Parece que estás teniendo problemas con Windows PowerShell, por favor consulta esta"}, "autoApprove": {"title": "Auto-aprobar:", "none": "<PERSON><PERSON><PERSON>", "description": "Auto-aprobar permite a cubent Code realizar acciones sin pedir permiso. Habilita solo para acciones en las que confíes plenamente. Configuración más detallada disponible en <settingsLink>Configuración</settingsLink>."}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "errorHeader": "Error al condensar el contexto", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a la entrada (o Shift + clic)"}, "announcement": {"title": "🎉 cubent Code {{version}} publicado", "description": "cubent Code {{version}} trae potentes nuevas funcionalidades y mejoras basadas en tus comentarios.", "whatsNew": "Novedades", "feature1": "<bold>Condensación Inteligente de Contexto Habilitada por Defecto</bold>: La condensación de contexto ahora está habilitada por defecto con configuraciones ajustables para cuando ocurre la condensación automática", "feature2": "<bold>Botón de Condensación Manual</bold>: Nuevo botón en el encabezado de tareas te permite activar manualmente la condensación de contexto en cualquier momento", "feature3": "<bold>Configuraciones de Condensación Mejoradas</bold>: <PERSON><PERSON><PERSON> cuán<PERSON> y cómo ocurre la condensación automática a través de la <contextSettingsLink>Configuración de Contexto</contextSettingsLink>", "hideButton": "<PERSON><PERSON><PERSON><PERSON> anuncio", "detailsDiscussLinks": "Obtén más detalles y participa en <discordLink>Discord</discordLink> y <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "cubent quiere usar el navegador:", "consoleLogs": "Registros de la consola", "noNewLogs": "(No hay nuevos registros)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Paso {{current}} de {{total}}", "previous": "Anterior", "next": "Siguient<PERSON>"}, "sessionStarted": "Sesión de navegador iniciada", "actions": {"title": "Acción de navegación: ", "launch": "Iniciar <PERSON> en {{url}}", "click": "Clic ({{coordinate}})", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> hacia arriba", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloque de código", "collapse": "Contraer bloque de código", "enable_wrap": "Activar ajuste de línea", "disable_wrap": "Desactivar ajuste de línea", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "systemPromptWarning": "ADVERTENCIA: Anulación de instrucciones del sistema personalizada activa. Esto puede romper gravemente la funcionalidad y causar un comportamiento impredecible.", "profileViolationWarning": "El perfil actual infringe la configuración de tu organización", "shellIntegration": {"title": "Advertencia de ejecución de comandos", "description": "Tu comando se está ejecutando sin la integración de shell de terminal de VSCode. Para suprimir esta advertencia, puedes desactivar la integración de shell en la sección <strong>Terminal</strong> de la <settingsLink>configuración de cubent Code</settingsLink> o solucionar problemas de integración de terminal de VSCode usando el enlace de abajo.", "troubleshooting": "Haz clic aquí para ver la documentación de integración de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Límite de Solicitudes Auto-aprobadas Alcanzado", "description": "cubent ha alcanzado el límite auto-aprobado de {{count}} solicitud(es) API. ¿Deseas reiniciar el contador y continuar con la tarea?", "button": "Reiniciar y Continuar"}}, "codebaseSearch": {"wantsToSearch": "cubent quiere buscar en la base de código <code>{{query}}</code>:", "wantsToSearchWithPath": "cubent quiere buscar en la base de código <code>{{query}}</code> en <code>{{path}}</code>:", "didSearch": "Se encontraron {{count}} resultado(s) para <code>{{query}}</code>:"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> todo"}, "deny": {"title": "<PERSON><PERSON><PERSON> todo"}}}