// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String    @id @default(cuid())
  clerkId            String    @unique
  email              String    @unique
  name               String?
  picture            String?
  subscriptionTier   String    @default("free")
  subscriptionStatus String    @default("active")
  cubentUnitsUsed    Int       @default(0)
  cubentUnitsLimit   Int       @default(50)
  unitsResetDate     DateTime?
  extensionEnabled   Boolean   @default(true)
  termsAccepted      <PERSON>ole<PERSON>   @default(false)
  lastActiveAt       DateTime?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  // Relations
  usageAnalytics   UsageAnalytics[]
  extensionSessions ExtensionSession[]

  @@map("users")
}

model UsageAnalytics {
  id              String   @id @default(cuid())
  userId          String
  modelId         String
  tokensUsed      Int      @default(0)
  cubentUnitsUsed Int      @default(0)
  requestsMade    Int      @default(1)
  costAccrued     Float    @default(0)
  sessionId       String?
  metadata        Json?
  createdAt       DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("usage_analytics")
}

model ExtensionSession {
  id               String    @id @default(cuid())
  userId           String
  sessionId        String    @unique
  isActive         Boolean   @default(true)
  lastActiveAt     DateTime  @default(now())
  extensionVersion String?
  vscodeVersion    String?
  platform         String?
  tokensUsed       Int       @default(0)
  requestsMade     Int       @default(0)
  metadata         Json?
  createdAt        DateTime  @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("extension_sessions")
}
