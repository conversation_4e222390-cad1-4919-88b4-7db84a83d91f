{"name": "@evals/monorepo", "private": true, "packageManager": "pnpm@10.8.1", "scripts": {"lint": "turbo lint --log-order grouped --output-logs new-only", "check-types": "turbo check-types --log-order grouped --output-logs new-only", "test": "turbo test --log-order grouped --output-logs new-only", "format": "turbo format --log-order grouped --output-logs new-only", "build": "turbo build --log-order grouped --output-logs new-only", "web": "turbo dev --filter @evals/web", "cli": "turbo dev --filter @evals/cli -- run", "drizzle:studio": "pnpm --filter @evals/db db:studio", "docker:build": "docker build -f Dockerfile -t cubent-eval --progress=plain ..", "docker:run": "touch /tmp/evals.db && docker run -d -it -p 3000:3000 -v /tmp/evals.db:/tmp/evals.db roo-code-eval", "docker:start": "pnpm docker:build && pnpm docker:run", "docker:shell": "docker exec -it $(docker ps --filter \"ancestor=roo-code-eval\" -q) /bin/bash", "docker:stop": "docker stop $(docker ps --filter \"ancestor=roo-code-eval\" -q)", "docker:rm": "docker rm $(docker ps -a --filter \"ancestor=roo-code-eval\" -q)", "docker:clean": "pnpm docker:stop && pnpm docker:rm"}, "devDependencies": {"@dotenvx/dotenvx": "^1.41.0", "@eslint/js": "^9.25.1", "eslint": "^9.25.1", "globals": "^16.0.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "turbo": "^2.5.2", "typescript": "5.8.3", "typescript-eslint": "^8.31.1"}}