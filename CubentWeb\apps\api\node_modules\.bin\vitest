#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/vitest@3.1.4_@types+debug@4_9c4f2f9600fc2907e16fa5e146b4ff07/node_modules/vitest/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/vitest@3.1.4_@types+debug@4_9c4f2f9600fc2907e16fa5e146b4ff07/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/vitest@3.1.4_@types+debug@4_9c4f2f9600fc2907e16fa5e146b4ff07/node_modules/vitest/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/vitest@3.1.4_@types+debug@4_9c4f2f9600fc2907e16fa5e146b4ff07/node_modules:/proc/cygdrive/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/CubentWeb/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
