{"title": "Modos", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modes": {"title": "Modos", "createNewMode": "Criar novo modo", "editModesConfig": "Editar configuração de modos", "editGlobalModes": "Editar modos globais", "editProjectModes": "Editar modos do projeto (.roomodes)", "createModeHelpText": "Modos são personas especializadas que adaptam o comportamento do cubent. <0>Saiba mais sobre o uso de modos</0> ou <1>Personalização de modos.</1>", "selectMode": "Buscar modos"}, "apiConfiguration": {"title": "Configuração de API", "select": "Selecione qual configuração de API usar para este modo"}, "tools": {"title": "Ferramentas disponíveis", "builtInModesText": "Ferramentas para modos integrados não podem ser modificadas", "editTools": "<PERSON><PERSON> fer<PERSON>", "doneEditing": "Concluir edição", "allowedFiles": "Arquivos permitidos:", "toolNames": {"read": "<PERSON><PERSON> a<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "browser": "<PERSON><PERSON>", "command": "Executar comandos", "mcp": "Usar MCP"}, "noTools": "<PERSON><PERSON><PERSON><PERSON>"}, "roleDefinition": {"title": "Definição de função", "resetToDefault": "Restaurar para padrão", "description": "Defina a expertise e personalidade do cubent para este modo. Esta descrição molda como o cubent se apresenta e aborda tarefas."}, "whenToUse": {"title": "Quando usar (opcional)", "description": "Descreva quando este modo deve ser usado. <PERSON><PERSON> a<PERSON> o Orchestrator a escolher o modo certo para uma tarefa.", "resetToDefault": "Restaurar descrição 'Quando usar' para padrão"}, "customInstructions": {"title": "Instruções personalizadas específicas do modo (opcional)", "resetToDefault": "Restaurar para padrão", "description": "Adicione diretrizes comportamentais específicas para o modo {{modeName}}.", "loadFromFile": "Instruções personalizadas específicas para o modo {{mode}} também podem ser carregadas da pasta <span>.cubent/rules-{{slug}}/</span> no seu espaço de trabalho (.roorules-{{slug}} e .clinerules-{{slug}} estão obsoletos e deixarão de funcionar em breve)."}, "globalCustomInstructions": {"title": "Instruções personalizadas para todos os modos", "description": "Estas instruções se aplicam a todos os modos. Elas fornecem um conjunto base de comportamentos que podem ser aprimorados por instruções específicas do modo abaixo. <0>Saiba mais</0>", "loadFromFile": "As instruções também podem ser carregadas da pasta <span>.cubent/rules/</span> no seu espaço de trabalho (.roorules e .clinerules estão obsoletos e deixarão de funcionar em breve)."}, "systemPrompt": {"preview": "Visualizar prompt do sistema", "copy": "<PERSON><PERSON><PERSON> prompt do sistema para a área de transferência", "title": "Prompt do sistema (modo {{modeName}})"}, "supportPrompts": {"title": "Prompts de suporte", "resetPrompt": "Restaurar prompt {{promptType}} para padrão", "prompt": "Prompt", "enhance": {"apiConfiguration": "Configuração de API", "apiConfigDescription": "Você pode selecionar uma configuração de API para usar sempre para aprimorar prompts, ou simplesmente use o que está atualmente selecionado", "useCurrentConfig": "Usar configuração de API atualmente selecionada", "testPromptPlaceholder": "Digite um prompt para testar o aprimoramento", "previewButton": "Visualizar aprimoramento do prompt", "testEnhancement": "Testar aprimoramento"}, "types": {"ENHANCE": {"label": "Apr<PERSON><PERSON> Prompt", "description": "Use o aprimoramento de prompt para obter sugestões ou melhorias personalizadas para suas entradas. <PERSON><PERSON> garante que o cubent entenda sua intenção e forneça as melhores respostas possíveis. Disponível através do ícone ✨ no chat."}, "EXPLAIN": {"label": "Explicar Código", "description": "Obtenha explicações detalhadas de trechos de código, funções ou arquivos inteiros. Útil para entender código complexo ou aprender novos padrões. Disponível nas ações de código (ícone de lâmpada no editor) e no menu de contexto do editor (clique direito no código selecionado)."}, "FIX": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Obtenha ajuda para identificar e resolver bugs, erros ou problemas de qualidade de código. Fornece orientação passo a passo para corrigir problemas. Disponível nas ações de código (ícone de lâmpada no editor) e no menu de contexto do editor (clique direito no código selecionado)."}, "IMPROVE": {"label": "<PERSON><PERSON><PERSON>", "description": "Receba sugestões para otimização de código, melhores práticas e melhorias arquitetônicas mantendo la funcionalidade. Disponível nas ações de código (ícone de lâmpada no editor) e no menu de contexto do editor (clique direito no código selecionado)."}, "ADD_TO_CONTEXT": {"label": "Adicionar ao Contexto", "description": "Adicione contexto à sua tarefa ou conversa atual. Útil para fornecer informações adicionais ou esclarecimentos. Disponível nas ações de código (ícone de lâmpada no editor) e no menu de contexto do editor (clique direito no código selecionado)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Adicionar <PERSON>teúdo do Terminal ao Contexto", "description": "Adicione a saída do terminal à sua tarefa ou conversa atual. Útil para fornecer saídas de comandos ou logs. Disponível no menu de contexto do terminal (clique direito no conteúdo selecionado do terminal)."}, "TERMINAL_FIX": {"label": "Corrigir Comando do Terminal", "description": "Obtenha ajuda para corrigir comandos de terminal que falharam ou precisam de melhorias. Disponível no menu de contexto do terminal (clique direito no conteúdo selecionado do terminal)."}, "TERMINAL_EXPLAIN": {"label": "Explicar Comando do Terminal", "description": "Obtenha explicações detalhadas de comandos de terminal e suas saídas. Disponível no menu de contexto do terminal (clique direito no conteúdo selecionado do terminal)."}, "NEW_TASK": {"label": "Iniciar <PERSON> Tarefa", "description": "Inicie uma nova tarefa com a entrada fornecida. Disponível na paleta de comandos."}}}, "advancedSystemPrompt": {"title": "Avançado: Substituir prompt do sistema", "description": "<2>⚠️ Aviso:</2> Este recurso avançado ignora as proteções. <1>LEIA ISTO ANTES DE USAR!</1>Substitua o prompt do sistema padrão criando um arquivo em <span>.cubent/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Criar novo modo", "close": "<PERSON><PERSON><PERSON>", "name": {"label": "Nome", "placeholder": "Digite o nome do modo"}, "slug": {"label": "Slug", "description": "O slug é usado em URLs e nomes de arquivos. Deve estar em minúsculas e conter apenas letras, números e hífens."}, "saveLocation": {"label": "Local de salvamento", "description": "Escolha onde salvar este modo. Os modos específicos do projeto têm precedência sobre os modos globais.", "global": {"label": "Global", "description": "Disponível em todos os espaços de trabalho"}, "project": {"label": "Específico do projeto (.roomodes)", "description": "Disponível apenas neste espaço de trabalho, tem precedência sobre o global"}}, "roleDefinition": {"label": "Definição de função", "description": "Defina a expertise e personalidade do cubent para este modo."}, "whenToUse": {"label": "Quando usar (opcional)", "description": "Forneça uma descrição clara de quando este modo é mais eficaz e para quais tipos de tarefas ele se destaca."}, "tools": {"label": "Ferramentas disponíveis", "description": "Selecione quais ferramentas este modo pode usar."}, "customInstructions": {"label": "Instruções personalizadas (opcional)", "description": "Adicione diretrizes comportamentais específicas para este modo."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>"}, "deleteMode": "Excluir modo"}, "allFiles": "todos os arquivos"}