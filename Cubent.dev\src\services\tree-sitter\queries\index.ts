export { solidityQuery } from "./solidity"
export { default as phpQuery } from "./php"
export { vueQuery } from "./vue"
export { default as typescriptQuery } from "./typescript"
export { default as tsxQuery } from "./tsx"
export { default as pythonQuery } from "./python"
export { default as javascriptQuery } from "./javascript"
export { default as javaQuery } from "./java"
export { default as rustQuery } from "./rust"
export { default as rubyQuery } from "./ruby"
export { default as cppQuery } from "./cpp"
export { default as cQuery } from "./c"
export { default as csharpQuery } from "./c-sharp"
export { default as goQuery } from "./go"
export { default as swiftQuery } from "./swift"
export { default as kotlinQuery } from "./kotlin"
export { default as cssQuery } from "./css"
export { default as elixirQuery } from "./elixir"
export { default as htmlQuery } from "./html"
export { default as luaQuery } from "./lua"
export { ocamlQuery } from "./ocaml"
export { tomlQuery } from "./toml"
export { default as systemrdlQuery } from "./systemrdl"
export { default as tlaPlusQuery } from "./tlaplus"
export { zigQuery } from "./zig"
export { default as embeddedTemplateQuery } from "./embedded_template"
export { elispQuery } from "./elisp"
export { scalaQuery } from "./scala"
