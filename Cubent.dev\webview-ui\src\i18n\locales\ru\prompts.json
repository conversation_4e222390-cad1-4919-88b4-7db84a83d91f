{"title": "Режимы", "done": "Готово", "modes": {"title": "Режимы", "createNewMode": "Создать новый режим", "editModesConfig": "Редактировать конфигурацию режимов", "editGlobalModes": "Редактировать глобальные режимы", "editProjectModes": "Редактировать режимы проекта (.roomodes)", "createModeHelpText": "Режимы — это специализированные персоны, которые адаптируют поведение cubent. <0>Узнайте об использовании режимов</0> или <1>настройке режимов.</1>", "selectMode": "Поиск режимов"}, "apiConfiguration": {"title": "Конфигурация API", "select": "Выберите, какую конфигурацию API использовать для этого режима"}, "tools": {"title": "Доступные инструменты", "builtInModesText": "Инструменты для встроенных режимов нельзя изменять", "editTools": "Редактировать инструменты", "doneEditing": "Завершить редактирование", "allowedFiles": "Разрешённые файлы:", "toolNames": {"read": "Чтение файлов", "edit": "Редактирование файлов", "browser": "Использовать браузер", "command": "Выполнять команды", "mcp": "Использовать MCP"}, "noTools": "Отсутствуют"}, "roleDefinition": {"title": "Определение роли", "resetToDefault": "Сбросить по умолчанию", "description": "Определите экспертность и личность cubent для этого режима. Это описание формирует, как cubent будет себя вести и выполнять задачи."}, "whenToUse": {"title": "Когда использовать (необязательно)", "description": "Опишите, когда следует использовать этот режим. Это помогает Orchestrator выбрать правильный режим для задачи.", "resetToDefault": "Сбросить описание 'Когда использовать' по умолчанию"}, "customInstructions": {"title": "Пользовательские инструкции для режима (необязательно)", "resetToDefault": "Сбросить по умолчанию", "description": "Добавьте рекомендации по поведению, специфичные для режима {{modeName}}.", "loadFromFile": "Пользовательские инструкции для режима {{mode}} также можно загрузить из папки <span>.cubent/rules-{{slug}}/</span> в вашем рабочем пространстве (.roorules-{{slug}} и .clinerules-{{slug}} устарели и скоро перестанут работать)."}, "globalCustomInstructions": {"title": "Пользовательские инструкции для всех режимов", "description": "Эти инструкции применяются ко всем режимам. Они задают базовое поведение, которое можно расширить с помощью инструкций ниже. <0>Узнать больше</0>", "loadFromFile": "Инструкции также можно загрузить из папки <span>.cubent/rules/</span> в вашем рабочем пространстве (.roorules и .clinerules устарели и скоро перестанут работать)."}, "systemPrompt": {"preview": "Предпросмотр системного промпта", "copy": "Скопировать системный промпт в буфер обмена", "title": "Системный промпт (режим {{modeName}})"}, "supportPrompts": {"title": "Вспомогательные промпты", "resetPrompt": "Сбросить промпт {{promptType}} по умолчанию", "prompt": "Промпт", "enhance": {"apiConfiguration": "Конфигурация API", "apiConfigDescription": "Вы можете выбрать конфигурацию API, которая всегда будет использоваться для улучшения промптов, или использовать текущую выбранную", "useCurrentConfig": "Использовать текущую конфигурацию API", "testPromptPlaceholder": "Введите промпт для тестирования улучшения", "previewButton": "Просмотреть улучшенный промпт", "testEnhancement": "Тестировать улучшение"}, "types": {"ENHANCE": {"label": "Улучшить промпт", "description": "Используйте улучшение промпта для получения индивидуальных предложений или улучшений ваших запросов. Это гарантирует, что cubent правильно поймет ваш запрос и даст лучший ответ. Доступно через ✨ в чате."}, "EXPLAIN": {"label": "Объяснить код", "description": "Получите подробные объяснения фрагментов кода, функций или целых файлов. Полезно для понимания сложного кода или изучения новых паттернов. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "FIX": {"label": "Исправить ошибки", "description": "Получите помощь в выявлении и исправлении багов, ошибок или проблем с качеством кода. Пошаговое руководство по устранению проблем. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "IMPROVE": {"label": "Улучшить код", "description": "Получите предложения по оптимизации кода, лучшим практикам и архитектурным улучшениям при сохранении функциональности. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "ADD_TO_CONTEXT": {"label": "Добавить в контекст", "description": "Добавьте контекст к текущей задаче или беседе. Полезно для предоставления дополнительной информации или пояснений. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Добавить содержимое терминала в контекст", "description": "Добавьте вывод терминала к текущей задаче или беседе. Полезно для предоставления результатов команд или логов. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "TERMINAL_FIX": {"label": "Исправить команду терминала", "description": "Получите помощь в исправлении команд терминала, которые не сработали или требуют улучшения. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "TERMINAL_EXPLAIN": {"label": "Объяснить команду терминала", "description": "Получите подробные объяснения команд терминала и их вывода. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "NEW_TASK": {"label": "Начать новую задачу", "description": "Начать новую задачу с пользовательским вводом. Доступно в палитре команд."}}}, "advancedSystemPrompt": {"title": "Дополнительно: переопределить системный промпт", "description": "<2>⚠️ Внимание:</2> Эта расширенная функция обходит средства защиты. <1>ПРОЧТИТЕ ЭТО ПЕРЕД ИСПОЛЬЗОВАНИЕМ!</1>Переопределите системный промпт по умолчанию, создав файл в <span>.cubent/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Создать новый режим", "close": "Закрыть", "name": {"label": "Название", "placeholder": "Введите название режима"}, "slug": {"label": "Слаг", "description": "Слаг используется в URL и именах файлов. Он должен быть в нижнем регистре и содержать только буквы, цифры и дефисы."}, "saveLocation": {"label": "Место сохранения", "description": "Выберите, где сохранить этот режим. Режимы проекта имеют приоритет над глобальными.", "global": {"label": "Глобально", "description": "Доступно во всех рабочих пространствах"}, "project": {"label": "Для проекта (.roomodes)", "description": "Доступно только в этом рабочем пространстве, имеет приоритет над глобальными"}}, "roleDefinition": {"label": "Определение роли", "description": "Определите экспертность и личность cubent для этого режима."}, "whenToUse": {"label": "Когда использовать (необязательно)", "description": "Предоставьте четкое описание, когда этот режим наиболее эффективен и для каких типов задач он лучше всего подходит."}, "tools": {"label": "Доступные инструменты", "description": "Выберите, какие инструменты может использовать этот режим."}, "customInstructions": {"label": "Пользовательские инструкции (необязательно)", "description": "Добавьте рекомендации по поведению, специфичные для этого режима."}, "buttons": {"cancel": "Отмена", "create": "Создать режим"}, "deleteMode": "Удалить режим"}, "allFiles": "все файлы"}