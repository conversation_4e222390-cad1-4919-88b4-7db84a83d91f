{"title": "MCP-servers", "done": "<PERSON><PERSON><PERSON>", "description": "<PERSON>hak<PERSON> het Model Context Protocol (MCP) in zodat cubent Code extra tools en diensten van externe servers kan gebruiken. Zo kan cubent meer voor je doen. <0>Meer informatie</0>", "enableToggle": {"title": "MCP-servers inschakelen", "description": "Zet dit AAN zodat cubent tools van verbonden MCP-servers kan gebruiken. Dit geeft cubent meer mogelijkheden. Gebruik je deze extra tools niet, zet het dan UIT om API-tokenkosten te besparen."}, "enableServerCreation": {"title": "MCP-server aanmaken inschakelen", "description": "<PERSON><PERSON><PERSON> dit in zodat cubent je kan helpen <1>nieuwe</1> aangepaste MCP-servers te bouwen. <0>Meer over server aanmaken</0>", "hint": "Tip: <PERSON><PERSON> deze instelling uit als je cubent niet actief vraagt om een nieuwe MCP-server te maken, om API-tokenkosten te besparen."}, "editGlobalMCP": "Globale MCP bewerken", "editProjectMCP": "Project-MCP bewerken", "learnMoreEditingSettings": "<PERSON><PERSON> over het bewerken van MCP-instellingen", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON><PERSON>", "parameters": "Parameters", "noDescription": "<PERSON><PERSON>"}, "tabs": {"tools": "Tools", "resources": "B<PERSON>nen", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "Geen tools gevonden", "noResources": "<PERSON>n bronnen gevonden", "noLogs": "<PERSON><PERSON> gevonden", "noErrors": "<PERSON><PERSON> fouten gevonden"}, "networkTimeout": {"label": "Netwerk-timeout", "description": "Maximale wachttijd op serverreacties", "options": {"15seconds": "15 seconden", "30seconds": "30 seconden", "1minute": "1 minuut", "5minutes": "5 minuten", "10minutes": "10 minuten", "15minutes": "15 minuten", "30minutes": "30 minuten", "60minutes": "60 minuten"}}, "deleteDialog": {"title": "MCP-server verwijderen", "description": "Weet je zeker dat je de MCP-server \"{{serverName}}\" wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Verwijderen"}, "serverStatus": {"retrying": "Opnieuw proberen...", "retryConnection": "Verbinding opnieuw proberen"}}