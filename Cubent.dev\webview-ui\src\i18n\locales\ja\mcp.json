{"title": "MCPサーバー", "done": "完了", "description": "Model Context Protocol (MCP) を有効にすると、cubent Code が外部サーバーから追加のツールやサービスを利用できるようになります。これで cubent ができることが広がるよ。<0>詳細はこちら</0>", "enableToggle": {"title": "MCPサーバーを有効化", "description": "これをONにすると、Rooが接続されたMCPサーバーのツールを使えるようになるよ。Rooの機能が増える！追加ツールを使わないなら、APIトークンのコストを抑えるためにOFFにしてね。"}, "enableServerCreation": {"title": "MCPサーバー作成を有効化", "description": "これをONにすると、Rooが<1>新しい</1>カスタムMCPサーバーを作るのを手伝ってくれるよ。<0>サーバー作成について詳しく</0>", "hint": "ヒント: APIトークンのコストを抑えたいときは、Rooに新しいMCPサーバーを作らせないときにこの設定をOFFにしてね。"}, "editGlobalMCP": "グローバルMCPを編集", "editProjectMCP": "プロジェクトMCPを編集", "learnMoreEditingSettings": "MCP設定ファイルの編集方法を詳しく見る", "tool": {"alwaysAllow": "常に許可", "parameters": "パラメーター", "noDescription": "説明なし"}, "tabs": {"tools": "ツール", "resources": "リソース", "errors": "エラー"}, "emptyState": {"noTools": "ツールが見つかりません", "noResources": "リソースが見つかりません", "noLogs": "ログが見つかりません", "noErrors": "エラーが見つかりません"}, "networkTimeout": {"label": "ネットワークタイムアウト", "description": "サーバー応答の最大待機時間", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分", "5minutes": "5分", "10minutes": "10分", "15minutes": "15分", "30minutes": "30分", "60minutes": "60分"}}, "deleteDialog": {"title": "MCPサーバーを削除", "description": "本当にMCPサーバー「{{serverName}}」を削除する？この操作は元に戻せないよ。", "cancel": "キャンセル", "delete": "削除"}, "serverStatus": {"retrying": "再試行中...", "retryConnection": "再接続"}}