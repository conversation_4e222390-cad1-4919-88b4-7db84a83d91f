{"extension.displayName": "cubent Code (trước đây là cubent Cline)", "extension.description": "<PERSON><PERSON>t đội ngũ phát triển các tác nhân AI hoàn chỉnh trong trình soạn thảo của bạn.", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "command.openInNewTab.title": "Mở trong Tab Mới", "command.focusInput.title": "<PERSON>ậ<PERSON>rung vào <PERSON>", "command.setCustomStoragePath.title": "Đặt Đường Dẫn Lưu Trữ Tùy Chỉnh", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON>m <PERSON>i Dung Terminal vào Ngữ Cảnh", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.acceptInput.title": "<PERSON>ấ<PERSON>/<PERSON><PERSON><PERSON> Ý", "views.activitybar.title": "cubent Code", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent Code", "command.mcpServers.title": "<PERSON><PERSON><PERSON> MCP", "command.prompts.title": "Chế Độ", "command.history.title": "<PERSON><PERSON><PERSON>", "command.openInEditor.title": "Mở trong Trình <PERSON>", "command.settings.title": "Cài Đặt", "command.documentation.title": "<PERSON><PERSON><PERSON>", "configuration.title": "cubent coder", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> l<PERSON>nh có thể được thực thi tự động khi 'Luôn phê duyệt các thao tác thực thi' đ<PERSON><PERSON><PERSON> bật", "settings.vsCodeLmModelSelector.description": "Cài đặt cho API mô hình ngôn ngữ VSCode", "settings.vsCodeLmModelSelector.vendor.description": "<PERSON><PERSON><PERSON> cung cấp mô hình ngôn ngữ (ví dụ: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON> mô hình ngôn ngữ (ví dụ: gpt-4)", "settings.customStoragePath.description": "Đường dẫn lưu trữ tùy chỉnh. Để trống để sử dụng vị trí mặc định. Hỗ trợ đường dẫn tuyệt đối (ví dụ: 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "Bật cubent coder Cloud."}