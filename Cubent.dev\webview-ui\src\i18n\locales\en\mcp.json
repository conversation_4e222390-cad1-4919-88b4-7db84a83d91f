{"title": "MCP Servers", "done": "Done", "description": "Enable the Model Context Protocol (MCP) to let cubent Code use extra tools and services from external servers. This expands what cubent can do for you. <0>Learn More</0>", "enableToggle": {"title": "Enable MCP Servers", "description": "Turn this ON to let cubent use tools from connected MCP servers. This gives cubent more capabilities. If you don't plan to use these extra tools, turn it OFF to help reduce API token costs."}, "enableServerCreation": {"title": "Enable MCP Server Creation", "description": "Enable this to have cubent help you build <1>new</1> custom MCP servers. <0>Learn about server creation</0>", "hint": "Hint: To reduce API token costs, disable this setting when you are not actively asking cubent to create a new MCP server."}, "editGlobalMCP": "Edit Global MCP", "editProjectMCP": "Edit Project MCP", "learnMoreEditingSettings": "Learn more about editing MCP settings files", "tool": {"alwaysAllow": "Always allow", "parameters": "Parameters", "noDescription": "No description"}, "tabs": {"tools": "Tools", "resources": "Resources", "errors": "Errors"}, "emptyState": {"noTools": "No tools found", "noResources": "No resources found", "noLogs": "No logs found", "noErrors": "No errors found"}, "networkTimeout": {"label": "Network Timeout", "description": "Maximum time to wait for server responses", "options": {"15seconds": "15 seconds", "30seconds": "30 seconds", "1minute": "1 minute", "5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes", "60minutes": "60 minutes"}}, "deleteDialog": {"title": "Delete MCP Server", "description": "Are you sure you want to delete the MCP server \"{{serverName}}\"? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "serverStatus": {"retrying": "Retrying...", "retryConnection": "Retry Connection"}}