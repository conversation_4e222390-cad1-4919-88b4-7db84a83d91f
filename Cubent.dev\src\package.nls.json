{"extension.displayName": "Cubent - AI Coding Agent & Autocomplete", "extension.description": "<PERSON><PERSON><PERSON> lives in your editor, helping you code faster and cleaner.", "views.contextMenu.label": "Send to Cubent", "views.terminalMenu.label": "Send to Cubent", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.newTask.title": "New Chat", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Modes", "command.history.title": "History", "command.openInEditor.title": "Open in Editor", "command.settings.title": "Settings", "command.autocomplete.title": "Autocomplete", "command.apiKey.title": "API Keys & Models", "command.documentation.title": "Documentation", "command.openInNewTab.title": "Open In New Tab", "command.explainCode.title": "Clarify This Code", "command.fixCode.title": "Fix with <PERSON><PERSON><PERSON>", "command.improveCode.title": "Refactor Code", "command.addToContext.title": "Send to Cubent", "command.focusInput.title": "Focus Input Field", "command.setCustomStoragePath.title": "Set Custom Storage Path", "command.terminal.addToContext.title": "Add Terminal Content to Context", "command.terminal.fixCommand.title": "Fix This Command", "command.terminal.explainCommand.title": "Explain This Command", "command.acceptInput.title": "Accept Input/Suggestion", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled", "settings.vsCodeLmModelSelector.description": "Settings for VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "The vendor of the language model (e.g. copilot)", "settings.vsCodeLmModelSelector.family.description": "The family of the language model (e.g. gpt-4)", "settings.customStoragePath.description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "Enable cubent Cloud."}