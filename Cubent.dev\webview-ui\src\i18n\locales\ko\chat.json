{"greeting": "cubent Code에 오신 것을 환영합니다", "chat": {"title": "채팅", "seeMore": "더 보기", "seeLess": "줄여보기", "tokens": "토큰:", "cache": "캐시:", "apiCost": "API 비용:", "contextWindow": "컨텍스트 창:", "closeAndStart": "채팅 닫고 새 채팅 시작", "export": "채팅 기록 내보내기", "delete": "채팅 삭제 (Shift + 클릭으로 확인 생략)", "condenseContext": "컨텍스트 지능적으로 압축"}, "unpin": "고정 해제하기", "pin": "고정하기", "tokenProgress": {"availableSpace": "사용 가능한 공간: {{amount}} 토큰", "tokensUsed": "사용된 토큰: {{used}} / {{total}}", "reservedForResponse": "모델 응답용 예약: {{amount}} 토큰"}, "retry": {"title": "다시 시도", "tooltip": "작업 다시 시도"}, "startNewChat": {"title": "새 채팅 시작", "tooltip": "새 채팅 시작하기"}, "proceedAnyways": {"title": "그래도 계속", "tooltip": "명령 실행 중에도 계속 진행"}, "save": {"title": "저장", "tooltip": "파일 변경사항 저장"}, "reject": {"title": "거부", "tooltip": "이 작업 거부"}, "completeSubtaskAndReturn": "하위 작업 완료 후 돌아가기", "approve": {"title": "승인", "tooltip": "이 작업 승인"}, "runCommand": {"title": "명령 실행", "tooltip": "이 명령 실행"}, "proceedWhileRunning": {"title": "실행 중에도 계속", "tooltip": "경고에도 불구하고 계속 진행"}, "killCommand": {"title": "명령 종료", "tooltip": "현재 명령 종료"}, "resumeTask": {"title": "작업 재개", "tooltip": "현재 작업 계속하기"}, "terminate": {"title": "종료", "tooltip": "현재 작업 종료"}, "cancel": {"title": "취소", "tooltip": "현재 작업 취소"}, "scrollToBottom": "채팅 하단으로 스크롤", "about": "AI 지원으로 코드를 생성, 리팩터링 및 디버깅합니다. 자세한 내용은 <DocsLink>문서</DocsLink>를 확인하세요.", "onboarding": "<strong>이 작업 공간의 작업 목록이 비어 있습니다.</strong> 아래에 작업을 입력하여 시작하세요. 어떻게 시작해야 할지 모르겠나요? Roo가 무엇을 할 수 있는지 <DocsLink>문서</DocsLink>에서 자세히 알아보세요.", "rooTips": {"boomerangTasks": {"title": "부메랑 작업", "description": "작업을 더 작고 관리하기 쉬운 부분으로 나눕니다."}, "stickyModels": {"title": "스티키 모드", "description": "각 모드는 마지막으로 사용한 모델을 기억합니다."}, "tools": {"title": "도구", "description": "AI가 웹 탐색, 명령 실행 등으로 문제를 해결하도록 허용합니다."}, "customizableModes": {"title": "사용자 정의 모드", "description": "고유한 동작과 할당된 모델을 가진 전문 페르소나"}}, "selectMode": "상호작용 모드 선택", "selectApiConfig": "API 구성 선택", "enhancePrompt": "추가 컨텍스트로 프롬프트 향상", "addImages": "메시지에 이미지 추가", "sendMessage": "메시지 보내기", "typeMessage": "메시지 입력...", "typeTask": "여기에 작업 입력...", "addContext": "컨텍스트 추가는 @, 모드 전환은 /", "dragFiles": "파일을 드래그하려면 shift 키 누르기", "dragFilesImages": "파일/이미지를 드래그하려면 shift 키 누르기", "enhancePromptDescription": "'프롬프트 향상' 버튼은 추가 컨텍스트, 명확화 또는 재구성을 제공하여 요청을 개선합니다. 여기에 요청을 입력한 다음 버튼을 다시 클릭하여 작동 방식을 확인해보세요.", "errorReadingFile": "파일 읽기 오류:", "noValidImages": "처리된 유효한 이미지가 없습니다", "separator": "구분자", "edit": "편집...", "forNextMode": "다음 모드용", "error": "오류", "diffError": {"title": "편집 실패"}, "troubleMessage": "Roo에 문제가 발생했습니다...", "apiRequest": {"title": "API 요청", "failed": "API 요청 실패", "streaming": "API 요청...", "cancelled": "API 요청 취소됨", "streamingFailed": "API 스트리밍 실패"}, "checkpoint": {"initial": "초기 체크포인트", "regular": "체크포인트", "initializingWarning": "체크포인트 초기화 중... 시간이 너무 오래 걸리면 <settingsLink>설정</settingsLink>에서 체크포인트를 비활성화하고 작업을 다시 시작할 수 있습니다.", "menu": {"viewDiff": "차이점 보기", "restore": "체크포인트 복원", "restoreFiles": "파일 복원", "restoreFilesDescription": "프로젝트 파일을 이 시점에 찍힌 스냅샷으로 복원합니다.", "restoreFilesAndTask": "파일 및 작업 복원", "confirm": "확인", "cancel": "취소", "cannotUndo": "이 작업은 취소할 수 없습니다.", "restoreFilesAndTaskDescription": "프로젝트 파일을 이 시점에 찍힌 스냅샷으로 복원하고 이 지점 이후의 모든 메시지를 삭제합니다."}, "current": "현재"}, "instructions": {"wantsToFetch": "Roo는 현재 작업을 지원하기 위해 자세한 지침을 가져오려고 합니다"}, "fileOperations": {"wantsToRead": "Roo가 이 파일을 읽고 싶어합니다:", "wantsToReadOutsideWorkspace": "Roo가 워크스페이스 외부의 이 파일을 읽고 싶어합니다:", "didRead": "Roo가 이 파일을 읽었습니다:", "wantsToEdit": "Roo가 이 파일을 편집하고 싶어합니다:", "wantsToEditOutsideWorkspace": "Roo가 워크스페이스 외부의 이 파일을 편집하고 싶어합니다:", "wantsToCreate": "Roo가 새 파일을 만들고 싶어합니다:", "wantsToSearchReplace": "Roo가 이 파일에서 검색 및 바꾸기를 수행하고 싶어합니다:", "didSearchReplace": "Roo가 이 파일에서 검색 및 바꾸기를 수행했습니다:", "wantsToInsert": "Roo가 이 파일에 내용을 삽입하고 싶어합니다:", "wantsToInsertWithLineNumber": "Roo가 이 파일의 {{lineNumber}}번 줄에 내용을 삽입하고 싶어합니다:", "wantsToInsertAtEnd": "Roo가 이 파일의 끝에 내용을 추가하고 싶어합니다:", "wantsToReadAndXMore": "Roo가 이 파일과 {{count}}개의 파일을 더 읽으려고 합니다:", "wantsToReadMultiple": "Roo가 여러 파일을 읽으려고 합니다:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo가 이 디렉토리의 최상위 파일을 보고 싶어합니다:", "didViewTopLevel": "Roo가 이 디렉토리의 최상위 파일을 보았습니다:", "wantsToViewRecursive": "Roo가 이 디렉토리의 모든 파일을 재귀적으로 보고 싶어합니다:", "didViewRecursive": "Roo가 이 디렉토리의 모든 파일을 재귀적으로 보았습니다:", "wantsToViewDefinitions": "Roo가 이 디렉토리에서 사용된 소스 코드 정의 이름을 보고 싶어합니다:", "didViewDefinitions": "Roo가 이 디렉토리에서 사용된 소스 코드 정의 이름을 보았습니다:", "wantsToSearch": "Roo가 이 디렉토리에서 <code>{{regex}}</code>을(를) 검색하고 싶어합니다:", "didSearch": "Roo가 이 디렉토리에서 <code>{{regex}}</code>을(를) 검색했습니다:"}, "commandOutput": "명령 출력", "response": "응답", "arguments": "인수", "mcp": {"wantsToUseTool": "Roo가 {{server<PERSON>ame}} MCP 서버에서 도구를 사용하고 싶어합니다:", "wantsToAccessResource": "Roo가 {{server<PERSON>ame}} MCP 서버에서 리소스에 접근하고 싶어합니다:"}, "modes": {"wantsToSwitch": "Roo가 <code>{{mode}}</code> 모드로 전환하고 싶어합니다", "wantsToSwitchWithReason": "Roo가 다음 이유로 <code>{{mode}}</code> 모드로 전환하고 싶어합니다: {{reason}}", "didSwitch": "Roo가 <code>{{mode}}</code> 모드로 전환했습니다", "didSwitchWithReason": "Roo가 다음 이유로 <code>{{mode}}</code> 모드로 전환했습니다: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo가 <code>{{mode}}</code> 모드에서 새 하위 작업을 만들고 싶어합니다:", "wantsToFinish": "Roo가 이 하위 작업을 완료하고 싶어합니다", "newTaskContent": "하위 작업 지침", "completionContent": "하위 작업 완료", "resultContent": "하위 작업 결과", "defaultResult": "다음 작업을 계속 진행해주세요.", "completionInstructions": "하위 작업 완료! 결과를 검토하고 수정 사항이나 다음 단계를 제안할 수 있습니다. 모든 것이 괜찮아 보이면, 부모 작업에 결과를 반환하기 위해 확인해주세요."}, "questions": {"hasQuestion": "명확화 대기 중:"}, "taskCompleted": "결과", "powershell": {"issues": "Windows PowerShell에 문제가 있는 것 같습니다. 다음을 참조하세요"}, "autoApprove": {"title": "자동 승인:", "none": "없음", "description": "자동 승인을 사용하면 cubent Code가 권한을 요청하지 않고 작업을 수행할 수 있습니다. 완전히 신뢰할 수 있는 작업에만 활성화하세요. 더 자세한 구성은 <settingsLink>설정</settingsLink>에서 사용할 수 있습니다."}, "reasoning": {"thinking": "생각 중", "seconds": "{{count}}초"}, "contextCondense": {"title": "컨텍스트 요약됨", "condensing": "컨텍스트 압축 중...", "errorHeader": "컨텍스트 압축 실패", "tokens": "토큰"}, "followUpSuggest": {"copyToInput": "입력창에 복사 (또는 Shift + 클릭)"}, "announcement": {"title": "🎉 cubent Code {{version}} 출시", "description": "cubent Code {{version}}은 사용자 피드백을 기반으로 강력한 새로운 기능과 개선사항을 제공합니다.", "whatsNew": "새로운 기능", "feature1": "<bold>지능형 컨텍스트 압축이 기본적으로 활성화됨</bold>: 컨텍스트 압축이 이제 기본적으로 활성화되며 자동 압축이 발생하는 시점을 구성할 수 있습니다", "feature2": "<bold>수동 압축 버튼</bold>: 작업 헤더의 새 버튼으로 언제든지 수동으로 컨텍스트 압축을 트리거할 수 있습니다", "feature3": "<bold>향상된 압축 설정</bold>: <contextSettingsLink>컨텍스트 설정</contextSettingsLink>을 통해 자동 압축이 언제 어떻게 발생하는지 세밀하게 조정", "hideButton": "공지 숨기기", "detailsDiscussLinks": "<discordLink>Discord</discordLink>와 <redditLink>Reddit</redditLink>에서 더 자세한 정보를 확인하고 논의하세요 🚀"}, "browser": {"rooWantsToUse": "Roo가 브라우저를 사용하고 싶어합니다:", "consoleLogs": "콘솔 로그", "noNewLogs": "(새 로그 없음)", "screenshot": "브라우저 스크린샷", "cursor": "커서", "navigation": {"step": "단계 {{current}} / {{total}}", "previous": "이전", "next": "다음"}, "sessionStarted": "브라우저 세션 시작됨", "actions": {"title": "브라우저 작업: ", "launch": "{{url}}에서 브라우저 실행", "click": "클릭 ({{coordinate}})", "type": "입력 \"{{text}}\"", "scrollDown": "아래로 스크롤", "scrollUp": "위로 스크롤", "close": "브라우저 닫기"}}, "codeblock": {"tooltips": {"expand": "코드 블록 확장", "collapse": "코드 블록 축소", "enable_wrap": "자동 줄바꿈 활성화", "disable_wrap": "자동 줄바꿈 비활성화", "copy_code": "코드 복사"}}, "systemPromptWarning": "경고: 사용자 정의 시스템 프롬프트 재정의가 활성화되었습니다. 이로 인해 기능이 심각하게 손상되고 예측할 수 없는 동작이 발생할 수 있습니다.", "profileViolationWarning": "현재 프로필이 조직 설정을 위반합니다", "shellIntegration": {"title": "명령 실행 경고", "description": "명령이 VSCode 터미널 쉘 통합 없이 실행되고 있습니다. 이 경고를 숨기려면 <settingsLink>cubent Code 설정</settingsLink>의 <strong>Terminal</strong> 섹션에서 쉘 통합을 비활성화하거나 아래 링크를 사용하여 VSCode 터미널 통합 문제를 해결하세요.", "troubleshooting": "쉘 통합 문서를 보려면 여기를 클릭하세요."}, "ask": {"autoApprovedRequestLimitReached": {"title": "자동 승인 요청 한도 도달", "description": "Roo가 {{count}}개의 API 요청(들)에 대한 자동 승인 한도에 도달했습니다. 카운트를 재설정하고 작업을 계속하시겠습니까?", "button": "재설정 후 계속"}}, "codebaseSearch": {"wantsToSearch": "Roo가 코드베이스에서 <code>{{query}}</code>을(를) 검색하고 싶어합니다:", "wantsToSearchWithPath": "Roo가 <code>{{path}}</code>에서 <code>{{query}}</code>을(를) 검색하고 싶어합니다:", "didSearch": "<code>{{query}}</code>에 대한 검색 결과 {{count}}개 찾음:"}, "read-batch": {"approve": {"title": "모두 승인"}, "deny": {"title": "모두 거부"}}}