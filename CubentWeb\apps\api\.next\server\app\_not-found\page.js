const CHUNK_PUBLIC_PATH = "server/app/_not-found/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_99c2c150._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/apps_api_app_f91e11fb._.js");
runtime.loadChunk("server/chunks/ssr/apps_api_app_fbdd0fd1._.js");
runtime.loadChunk("server/chunks/ssr/apps_api_app_a24434b0._.js");
runtime.loadChunk("server/chunks/ssr/_787c0db3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_6053f640._.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_client_components_forbidden-error_e549a34e.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_client_components_unauthorized-error_7dab06d3.js");
runtime.loadChunk("server/chunks/ssr/661a5_next_dist_51a0dd65._.js");
runtime.loadChunk("server/chunks/ssr/apps_api_b30e968f._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/api/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { GLOBAL_ERROR_MODULE => \"[project]/apps/api/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", METADATA_0 => \"[project]/apps/api/app/icon.png.mjs { IMAGE => \\\"[project]/apps/api/app/icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/apps/api/app/apple-icon.png.mjs { IMAGE => \\\"[project]/apps/api/app/apple-icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_2 => \"[project]/apps/api/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/apps/api/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_3 => \"[project]/apps/api/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/api/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { GLOBAL_ERROR_MODULE => \"[project]/apps/api/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", METADATA_0 => \"[project]/apps/api/app/icon.png.mjs { IMAGE => \\\"[project]/apps/api/app/icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/apps/api/app/apple-icon.png.mjs { IMAGE => \\\"[project]/apps/api/app/apple-icon.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_2 => \"[project]/apps/api/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/apps/api/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_3 => \"[project]/apps/api/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/apps/api/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
