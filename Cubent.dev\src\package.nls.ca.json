{"extension.displayName": "cubent coder", "extension.description": "Un equip complet de desenvolupament d'agents d'IA al teu editor.", "command.newTask.title": "Nova Tasca", "command.explainCode.title": "Explicar Codi", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "Millorar Codi", "command.addToContext.title": "<PERSON><PERSON><PERSON>r al Context", "command.openInNewTab.title": "Obrir en una Nova Pestanya", "command.focusInput.title": "Enfocar Camp d'Entrada", "command.setCustomStoragePath.title": "Establir Ruta d'Emmagatzematge Personalitzada", "command.terminal.addToContext.title": "Afegir Contingut del Terminal al Context", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar <PERSON><PERSON><PERSON>", "command.acceptInput.title": "Acceptar Entrada/Suggeriment", "views.activitybar.title": "cubent coder", "views.contextMenu.label": "Send to cubent", "views.terminalMenu.label": "Send to cubent", "views.sidebar.name": "cubent coder", "command.mcpServers.title": "Servidors MCP", "command.prompts.title": "Modes", "command.history.title": "Historial", "command.openInEditor.title": "Obrir a l'Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Documentació", "configuration.title": "cubent coder", "commands.allowedCommands.description": "Ordres que es poden executar automàticament quan 'Aprova sempre les operacions d'execució' està activat", "settings.vsCodeLmModelSelector.description": "Configuració per a l'API del model de llenguatge VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveïdor del model de llenguatge (p. ex. copilot)", "settings.vsCodeLmModelSelector.family.description": "La família del model de llenguatge (p. ex. gpt-4)", "settings.customStoragePath.description": "Ruta d'emmagatzematge personalitzada. Deixeu-la buida per utilitzar la ubicació predeterminada. Admet rutes absolutes (p. ex. 'D:\\cubentCoderStorage')", "settings.cubentCoderCloudEnabled.description": "Habilitar cubent coder Cloud."}