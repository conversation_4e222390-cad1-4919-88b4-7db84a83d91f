{"greeting": "Добро пожаловать в cubent Code", "task": {"title": "Задача", "seeMore": "Показать больше", "seeLess": "Показать меньше", "tokens": "Токенов:", "cache": "Кэш:", "apiCost": "Стоимость API:", "contextWindow": "<PERSON><PERSON><PERSON><PERSON> контекста:", "closeAndStart": "Закрыть задачу и начать новую", "export": "Экспортировать историю задач", "delete": "Удалить задачу (Shift + клик для пропуска подтверждения)", "condenseContext": "Интеллектуально сжать контекст"}, "unpin": "Открепить", "pin": "Закрепить", "retry": {"title": "Повторить", "tooltip": "Попробовать выполнить операцию снова"}, "startNewTask": {"title": "Начать новую задачу", "tooltip": "Начать новую задачу"}, "proceedAnyways": {"title": "Все равно продолжить", "tooltip": "Продолжить выполнение команды"}, "save": {"title": "Сохранить", "tooltip": "Сохранить изменения в файле"}, "tokenProgress": {"availableSpace": "Доступно места: {{amount}} токенов", "tokensUsed": "Использовано токенов: {{used}} из {{total}}", "reservedForResponse": "Зарезервировано для ответа модели: {{amount}} токенов"}, "reject": {"title": "Отклонить", "tooltip": "Отклонить это действие"}, "completeSubtaskAndReturn": "Завершить подзадачу и вернуться", "approve": {"title": "Одобрить", "tooltip": "Одобрить это действие"}, "runCommand": {"title": "Выполнить команду", "tooltip": "Выполнить эту команду"}, "proceedWhileRunning": {"title": "Продолжить во время выполнения", "tooltip": "Продолжить несмотря на предупреждения"}, "resumeTask": {"title": "Возобновить задачу", "tooltip": "Продолжить текущую задачу"}, "killCommand": {"title": "Завер<PERSON>ить команду", "tooltip": "Завершить текущую команду"}, "terminate": {"title": "Завершить", "tooltip": "Завершить текущую задачу"}, "cancel": {"title": "Отмена", "tooltip": "Отменить текущую операцию"}, "scrollToBottom": "Прокрутить чат вниз", "about": "Создавайте, рефакторите и отлаживайте код с помощью ИИ. Подробнее см. в нашей <DocsLink>документации</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Задачи-бумеранги", "description": "Разделяйте задачи на более мелкие, управляемые части"}, "stickyModels": {"title": "Липкие режимы", "description": "Каждый режим запоминает вашу последнюю использованную модель"}, "tools": {"title": "Инструменты", "description": "Разрешите ИИ решать проблемы, просматривая веб-страницы, выполняя команды и т. д."}, "customizableModes": {"title": "Настраиваемые режимы", "description": "Специализированные персонажи с собственным поведением и назначенными моделями"}}, "onboarding": "<strong>Ваш список задач в этом рабочем пространстве пуст.</strong> Начните с ввода задачи ниже. Не знаете, с чего начать? Подробнее о возможностях cubent читайте в <DocsLink>документации</DocsLink>.", "selectMode": "Выберите режим взаимодействия", "selectApiConfig": "Выберите конфигурацию API", "enhancePrompt": "Улучшить запрос с дополнительным контекстом", "enhancePromptDescription": "Кнопка 'Улучшить запрос' помогает сделать ваш запрос лучше, предоставляя дополнительный контекст, уточнения или переформулировку. Попробуйте ввести запрос и снова нажать кнопку, чтобы увидеть, как это работает.", "addImages": "Добавить изображения к сообщению", "sendMessage": "Отправить сообщение", "typeMessage": "Введите сообщение...", "typeTask": "Введите вашу задачу здесь...", "addContext": "@ для добавления контекста, / для смены режима", "dragFiles": "удерживайте shift для перетаскивания файлов", "dragFilesImages": "удерживайте shift для перетаскивания файлов/изображений", "errorReadingFile": "Ошибка чтения файла:", "noValidImages": "Не удалось обработать ни одно изображение", "separator": "Разделитель", "edit": "Редактировать...", "forNextMode": "для следующего режима", "apiRequest": {"title": "API-запрос", "failed": "API-запрос не выполнен", "streaming": "API-запрос...", "cancelled": "API-запрос отменен", "streamingFailed": "Ошибка потокового API-запроса"}, "checkpoint": {"initial": "Начальная точка сохранения", "regular": "Точка сохранения", "initializingWarning": "Точка сохранения еще инициализируется... Если это занимает слишком много времени, вы можете отключить точки сохранения в <settingsLink>настройках</settingsLink> и перезапустить задачу.", "menu": {"viewDiff": "Просмотреть различия", "restore": "Восстановить точку сохранения", "restoreFiles": "Восстановить файлы", "restoreFilesDescription": "Восстанавливает файлы вашего проекта до состояния на момент этой точки.", "restoreFilesAndTask": "Восстановить файлы и задачу", "confirm": "Подтвердить", "cancel": "Отмена", "cannotUndo": "Это действие нельзя отменить.", "restoreFilesAndTaskDescription": "Восстанавливает файлы проекта до состояния на момент этой точки и удаляет все сообщения после нее."}, "current": "Текущая"}, "instructions": {"wantsToFetch": "cubent хочет получить подробные инструкции для помощи с текущей задачей"}, "fileOperations": {"wantsToRead": "cubent хочет прочитать этот файл:", "wantsToReadOutsideWorkspace": "cubent хочет прочитать этот файл вне рабочей области:", "didRead": "cubent прочитал этот файл:", "wantsToEdit": "cubent хочет отредактировать этот файл:", "wantsToEditOutsideWorkspace": "cubent хочет отредактировать этот файл вне рабочей области:", "wantsToCreate": "cubent хочет создать новый файл:", "wantsToSearchReplace": "cubent хочет выполнить поиск и замену в этом файле:", "didSearchReplace": "cubent выполнил поиск и замену в этом файле:", "wantsToInsert": "cubent хочет вставить содержимое в этот файл:", "wantsToInsertWithLineNumber": "cubent хочет вставить содержимое в этот файл на строку {{lineNumber}}:", "wantsToInsertAtEnd": "cubent хочет добавить содержимое в конец этого файла:", "wantsToReadAndXMore": "cubent хочет прочитать этот файл и еще {{count}}:", "wantsToReadMultiple": "cubent хочет прочитать несколько файлов:"}, "directoryOperations": {"wantsToViewTopLevel": "cubent хочет просмотреть файлы верхнего уровня в этой директории:", "didViewTopLevel": "cubent просмотрел файлы верхнего уровня в этой директории:", "wantsToViewRecursive": "cubent хочет рекурсивно просмотреть все файлы в этой директории:", "didViewRecursive": "cubent рекурсивно просмотрел все файлы в этой директории:", "wantsToViewDefinitions": "cubent хочет просмотреть имена определений исходного кода в этой директории:", "didViewDefinitions": "cubent просмотрел имена определений исходного кода в этой директории:", "wantsToSearch": "cubent хочет выполнить поиск в этой директории по <code>{{regex}}</code>:", "didSearch": "cubent выполнил поиск в этой директории по <code>{{regex}}</code>:"}, "commandOutput": "Вывод команды", "response": "Ответ", "arguments": "Аргументы", "mcp": {"wantsToUseTool": "cubent хочет использовать инструмент на сервере MCP {{serverName}}:", "wantsToAccessResource": "cubent хочет получить доступ к ресурсу на сервере MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "cubent хочет переключиться в режим {{mode}}", "wantsToSwitchWithReason": "cubent хочет переключиться в режим {{mode}}, потому что: {{reason}}", "didSwitch": "cubent переключился в режим {{mode}}", "didSwitchWithReason": "cubent переключился в режим {{mode}}, потому что: {{reason}}"}, "subtasks": {"wantsToCreate": "cubent хочет создать новую подзадачу в режиме {{mode}}:", "wantsToFinish": "cubent хочет завершить эту подзадачу", "newTaskContent": "Инструкции по подзадаче", "completionContent": "Подзадача завершена", "resultContent": "Результаты подзадачи", "defaultResult": "Пожалуйста, переходите к следующей задаче.", "completionInstructions": "Подзадача завершена! Вы можете просмотреть результаты и предложить исправления или следующие шаги. Если всё в порядке, подтвердите для возврата результата в родительскую задачу."}, "questions": {"hasQuestion": "Ожидание уточнения:"}, "taskCompleted": "Результаты", "error": "Ошибка", "diffError": {"title": "Не удалось выполнить редактирование"}, "troubleMessage": "У cubent возникли проблемы...", "powershell": {"issues": "Похоже, у вас проблемы с Windows PowerShell, пожалуйста, ознакомьтесь с этим"}, "autoApprove": {"title": "Автоодобрение:", "none": "Нет", "description": "Автоодобрение позволяет cubent Code выполнять действия без запроса разрешения. Включайте только для полностью доверенных действий. Более подробная настройка доступна в <settingsLink>Настройках</settingsLink>."}, "announcement": {"title": "🎉 Выпущен cubent Code {{version}}", "description": "cubent Code {{version}} приносит мощные новые функции и улучшения на основе ваших отзывов.", "whatsNew": "Что нового", "feature1": "<bold>Интеллектуальное Сжатие Контекста Включено по Умолчанию</bold>: Сжатие контекста теперь включено по умолчанию с настраиваемыми параметрами для автоматического сжатия", "feature2": "<bold>Кнопка Ручного Сжатия</bold>: Новая кнопка в заголовке задачи позволяет вручную запускать сжатие контекста в любое время", "feature3": "<bold>Улучшенные Настройки Сжатия</bold>: Настройте когда и как происходит автоматическое сжатие через <contextSettingsLink>Настройки Контекста</contextSettingsLink>", "hideButton": "Скрыть объявление", "detailsDiscussLinks": "Подробнее и обсуждение в <discordLink>Discord</discordLink> и <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Обдумывание", "seconds": "{{count}}с"}, "contextCondense": {"title": "Контекст сжат", "condensing": "Сжатие контекста...", "errorHeader": "Не удалось сжать контекст", "tokens": "токены"}, "followUpSuggest": {"copyToInput": "Скопировать во ввод (то же, что shift + клик)"}, "browser": {"rooWantsToUse": "cubent хочет использовать браузер:", "consoleLogs": "Логи консоли", "noNewLogs": "(Новых логов нет)", "screenshot": "Скриншот браузера", "cursor": "курсор", "navigation": {"step": "Шаг {{current}} из {{total}}", "previous": "Предыдущий", "next": "Следующий"}, "sessionStarted": "Сессия браузера запущена", "actions": {"title": "Действие в браузере: ", "launch": "Открыть браузер по адресу {{url}}", "click": "Клик ({{coordinate}})", "type": "Ввести \"{{text}}\"", "scrollDown": "Прокрутить вниз", "scrollUp": "Прокрутить вверх", "close": "Закрыть браузер"}}, "codeblock": {"tooltips": {"expand": "Развернуть блок кода", "collapse": "Свернуть блок кода", "enable_wrap": "Включить перенос строк", "disable_wrap": "Отключить перенос строк", "copy_code": "Копировать код"}}, "systemPromptWarning": "ПРЕДУПРЕЖДЕНИЕ: Активна пользовательская системная подсказка. Это может серьезно нарушить работу и вызвать непредсказуемое поведение.", "profileViolationWarning": "Текущий профиль нарушает настройки вашей организации", "shellIntegration": {"title": "Предупреждение о выполнении команды", "description": "Ваша команда выполняется без интеграции оболочки терминала VSCode. Чтобы скрыть это предупреждение, вы можете отключить интеграцию оболочки в разделе <strong>Terminal</strong> в <settingsLink>настройках cubent Code</settingsLink> или устранить проблемы с интеграцией терминала VSCode, используя ссылку ниже.", "troubleshooting": "Нажмите здесь для просмотра документации по интеграции оболочки."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Достигнут лимит автоматически одобренных запросов", "description": "cubent достиг автоматически одобренного лимита в {{count}} API-запрос(ов). Хотите сбросить счетчик и продолжить задачу?", "button": "Сбросить и продолжить"}}, "codebaseSearch": {"wantsToSearch": "cubent хочет выполнить поиск в кодовой базе по <code>{{query}}</code>:", "wantsToSearchWithPath": "cubent хочет выполнить поиск в кодовой базе по <code>{{query}}</code> в <code>{{path}}</code>:", "didSearch": "Найдено {{count}} результат(ов) для <code>{{query}}</code>:"}, "read-batch": {"approve": {"title": "Одобрить все"}, "deny": {"title": "Отклонить все"}}}