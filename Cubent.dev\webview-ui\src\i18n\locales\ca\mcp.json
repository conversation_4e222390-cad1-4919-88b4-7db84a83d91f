{"title": "Servidors MCP", "done": "Fet", "description": "Activa el Model Context Protocol (MCP) perquè cubent Code pugui utilitzar eines i serveis addicionals de servidors externs. Això amplia el que cubent pot fer per tu. <0>Més informació</0>", "enableToggle": {"title": "Activa els servidors MCP", "description": "Activa-ho perquè cubent pugui utilitzar eines dels servidors MCP connectats. Això dóna més capacitats a cubent. Si no vols utilitzar aquestes eines addicionals, desactiva-ho per ajudar a reduir el cost dels tokens API."}, "enableServerCreation": {"title": "Activa la creació de servidors MCP", "description": "Activa-ho perquè cubent t'ajudi a crear <1>nous</1> servidors MCP personalitzats. <0>Més informació sobre la creació de servidors</0>", "hint": "Consell: Per reduir el cost dels tokens API, desactiva aquesta opció quan no demanis a cubent que creï un nou servidor MCP."}, "editGlobalMCP": "Edita MCP global", "editProjectMCP": "Edita MCP del projecte", "learnMoreEditingSettings": "Més informació sobre com editar fitxers de configuració MCP", "tool": {"alwaysAllow": "Permet sempre", "parameters": "Paràmetres", "noDescription": "Sense descripció"}, "tabs": {"tools": "<PERSON><PERSON>", "resources": "Recursos", "errors": "Errors"}, "emptyState": {"noTools": "No s'han trobat eines", "noResources": "No s'han trobat recursos", "noLogs": "No s'han trobat registres", "noErrors": "No s'han trobat errors"}, "networkTimeout": {"label": "Temps d'espera de xarxa", "description": "Temps màxim d'espera per a les respostes del servidor", "options": {"15seconds": "15 segons", "30seconds": "30 segons", "1minute": "1 minut", "5minutes": "5 minuts", "10minutes": "10 minuts", "15minutes": "15 minuts", "30minutes": "30 minuts", "60minutes": "60 minuts"}}, "deleteDialog": {"title": "Elimina el servidor MCP", "description": "Segur que vols eliminar el servidor MCP \"{{serverName}}\"? Aquesta acció no es pot desfer.", "cancel": "Cancel·la", "delete": "Elimina"}, "serverStatus": {"retrying": "Tornant a intentar...", "retryConnection": "Torna a intentar la connexió"}}