{"greeting": "Salut, je suis cubent !", "introduction": "<strong>cubent Code est l'agent de codage autonome de premier plan.</strong> Prépare-toi à architecturer, coder, déboguer et à augmenter ta productivité comme jamais auparavant. Pour continuer, cubent Code nécessite une clé API.", "notice": "Pour commencer, cette extension a besoin d'un fournisseur d'API.", "start": "C'est parti !", "chooseProvider": "Choisis un fournisseur d'API pour commencer :", "routers": {"requesty": {"description": "Ton routeur LLM optimisé", "incentive": "1$ de crédit gratuit"}, "openrouter": {"description": "Une interface unifiée pour les LLMs"}}, "startRouter": "Configuration rapide via un routeur", "startCustom": "Utiliser ta propre clé API", "telemetry": {"title": "Aide à améliorer cubent Code", "anonymousTelemetry": "Envoie des données d'utilisation et d'erreurs anonymes pour nous aider à corriger les bugs et améliorer l'extension. Aucun code, texte ou information personnelle n'est jamais envoyé.", "changeSettings": "Tu peux toujours modifier cela en bas des <settingsLink>paramètres</settingsLink>", "settings": "paramètres", "allow": "Autoriser", "deny": "Refuser"}, "or": "ou", "importSettings": "Importer les paramètres"}