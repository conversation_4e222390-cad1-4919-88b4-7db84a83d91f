// npx jest src/services/url-content/__tests__/UrlContentFetcher.test.ts

import { describe, expect, it, jest, beforeEach } from "@jest/globals"
import { UrlContentFetcher } from "../UrlContentFetcher"

// Mock fetch globally
global.fetch = jest.fn()

describe("UrlContentFetcher", () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe("validateUrl", () => {
		it("should validate correct HTTP URLs", async () => {
			const result = await UrlContentFetcher.validateUrl("https://example.com")
			expect(result.valid).toBe(true)
			expect(result.error).toBeUndefined()
		})

		it("should validate correct HTTPS URLs", async () => {
			const result = await UrlContentFetcher.validateUrl("http://example.com")
			expect(result.valid).toBe(true)
			expect(result.error).toBeUndefined()
		})

		it("should reject invalid URLs", async () => {
			const result = await UrlContentFetcher.validateUrl("not-a-url")
			expect(result.valid).toBe(false)
			expect(result.error).toBe("Invalid URL format")
		})

		it("should reject empty URLs", async () => {
			const result = await UrlContentFetcher.validateUrl("")
			expect(result.valid).toBe(false)
			expect(result.error).toBe("Invalid URL format")
		})
	})

	describe("fetchUrlContent", () => {
		it("should successfully fetch and process HTML content", async () => {
			const mockHtml = `
				<!DOCTYPE html>
				<html>
					<head><title>Test Page</title></head>
					<body>
						<article>
							<h1>Main Content</h1>
							<p>This is the main content of the page.</p>
						</article>
					</body>
				</html>
			`

			const mockResponse = {
				ok: true,
				status: 200,
				statusText: "OK",
				headers: new Map([["content-type", "text/html"]]),
				text: jest.fn().mockResolvedValue(mockHtml),
			}

			;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

			const result = await UrlContentFetcher.fetchUrlContent("https://example.com")

			expect(result.success).toBe(true)
			expect(result.url).toBe("https://example.com")
			expect(result.title).toBe("Test Page")
			expect(result.content).toContain("Main Content")
			expect(result.content).toContain("This is the main content")
		})

		it("should handle HTTP errors", async () => {
			const mockResponse = {
				ok: false,
				status: 404,
				statusText: "Not Found",
			}

			;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

			const result = await UrlContentFetcher.fetchUrlContent("https://example.com/not-found")

			expect(result.success).toBe(false)
			expect(result.error).toBe("HTTP 404: Not Found")
		})

		it("should handle network errors", async () => {
			;(global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"))

			const result = await UrlContentFetcher.fetchUrlContent("https://example.com")

			expect(result.success).toBe(false)
			expect(result.error).toBe("Network error")
		})

		it("should reject non-HTTP protocols", async () => {
			const result = await UrlContentFetcher.fetchUrlContent("ftp://example.com")

			expect(result.success).toBe(false)
			expect(result.error).toBe("Only HTTP and HTTPS URLs are supported")
		})

		it("should handle non-HTML content types", async () => {
			const mockResponse = {
				ok: true,
				status: 200,
				statusText: "OK",
				headers: new Map([["content-type", "application/json"]]),
			}

			;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

			const result = await UrlContentFetcher.fetchUrlContent("https://api.example.com/data.json")

			expect(result.success).toBe(false)
			expect(result.error).toContain("Unsupported content type")
		})

		it("should truncate very long content", async () => {
			const longContent = "a".repeat(25000) // Longer than MAX_CONTENT_LENGTH
			const mockHtml = `
				<!DOCTYPE html>
				<html>
					<head><title>Long Page</title></head>
					<body>
						<article>
							<p>${longContent}</p>
						</article>
					</body>
				</html>
			`

			const mockResponse = {
				ok: true,
				status: 200,
				statusText: "OK",
				headers: new Map([["content-type", "text/html"]]),
				text: jest.fn().mockResolvedValue(mockHtml),
			}

			;(global.fetch as jest.Mock).mockResolvedValue(mockResponse)

			const result = await UrlContentFetcher.fetchUrlContent("https://example.com")

			expect(result.success).toBe(true)
			expect(result.content).toContain("[Content truncated - original content was longer]")
			expect(result.content!.length).toBeLessThan(25000)
		})
	})
})
