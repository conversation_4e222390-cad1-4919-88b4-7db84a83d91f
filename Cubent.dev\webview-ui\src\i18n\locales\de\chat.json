{"greeting": "Willkommen bei cubent Code", "task": {"title": "Aufgabe", "seeMore": "<PERSON><PERSON> anzeigen", "seeLess": "<PERSON><PERSON> anzeigen", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-Kosten:", "contextWindow": "Kontextfenster:", "closeAndStart": "Aufgabe schließen und neue starten", "export": "Aufgabenverlauf exportieren", "delete": "Aufgabe löschen (Shift + Klick zum Überspringen der Bestätigung)", "condenseContext": "Kontext intelligent komprimieren"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "Anheften", "tokenProgress": {"availableSpace": "Verfügbarer S<PERSON>icher: {{amount}} Tokens", "tokensUsed": "Verwen<PERSON><PERSON> Tokens: {{used}} von {{total}}", "reservedForResponse": "Reserviert für Modellantwort: {{amount}} Tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Versuch erneut starten"}, "startNewTask": {"title": "Neue Aufgabe starten", "tooltip": "<PERSON><PERSON>ne eine neue Aufgabe"}, "proceedAnyways": {"title": "Trotzdem fortfahren", "tooltip": "Während der Befehlsausführung fortfahren"}, "save": {"title": "Speichern", "tooltip": "Dateiänderungen speichern"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion ablehnen"}, "completeSubtaskAndReturn": "Teilaufgabe abschließen und zurückkehren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion genehmigen"}, "runCommand": {"title": "Befehl ausführen", "tooltip": "<PERSON>sen Befehl ausführen"}, "proceedWhileRunning": {"title": "Während Ausführung fortfahren", "tooltip": "Trotz Warnungen fortfahren"}, "killCommand": {"title": "<PERSON><PERSON><PERSON> abbrechen", "tooltip": "Aktuellen Befehl abbrechen"}, "resumeTask": {"title": "Aufgabe fortsetzen", "tooltip": "Aktuelle Aufgabe fortsetzen"}, "terminate": {"title": "<PERSON>den", "tooltip": "Aktuelle Aufgabe beenden"}, "cancel": {"title": "Abbrechen", "tooltip": "Aktuelle Operation abbrechen"}, "scrollToBottom": "<PERSON><PERSON> Chat-<PERSON><PERSON> scrollen", "about": "Generiere, überarbeite und debugge Code mit KI-Unterstützung. Weitere Informationen findest du in unserer <DocsLink>Dokumentation</DocsLink>.", "onboarding": "<strong><PERSON><PERSON> in diesem Arbeitsbereich ist leer.</strong> Beginne mit der Eingabe einer Aufgabe unten. Du bist dir nicht sicher, wie du anfangen sollst? Lies mehr darüber, was cubent für dich tun kann, in <DocsLink>den Dokumenten</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Bumerang-Aufgaben", "description": "<PERSON><PERSON> Aufgaben in kleinere, überschaubare Teile auf."}, "stickyModels": {"title": "<PERSON><PERSON>", "description": "Jeder Modus merkt sich dein zuletzt verwendetes Modell"}, "tools": {"title": "Tools", "description": "Erlaube der KI, Probleme durch Surfen im Web, Ausführen von <PERSON>en und mehr zu lösen."}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Spezialisierte Personas mit eigenem Verhalten und zugewiesenen Modellen"}}, "selectMode": "Interaktionsmodus auswählen", "selectApiConfig": "API-Konfiguration auswählen", "enhancePrompt": "Prompt mit zusätzlichem Kontext verbessern", "addImages": "Bilder zur Nachricht hinzufügen", "sendMessage": "Nachricht senden", "typeMessage": "Nachricht eingeben...", "typeTask": "Gib deine Aufgabe hier ein...", "addContext": "@ für Kontext, / zum Moduswechsel", "dragFiles": "Shift halten, um Dateien einzufügen", "dragFilesImages": "Shift halten, um Dateien/Bilder einzufügen", "enhancePromptDescription": "Die Schaltfläche 'Prompt verbessern' hi<PERSON><PERSON>, deine Anfrage durch zusätzlichen Kontext, Klarstellungen oder Umformulierungen zu verbessern. <PERSON><PERSON><PERSON>, hier eine Anfrage einzugeben und klicke erneut auf die Schaltfläche, um zu sehen, wie es funktioniert.", "errorReadingFile": "<PERSON><PERSON> beim Lesen der Datei:", "noValidImages": "<PERSON><PERSON> gültigen Bilder wurden verarbeitet", "separator": "Trennlinie", "edit": "Bearbeiten...", "forNextMode": "für nächsten Modus", "error": "<PERSON><PERSON>", "diffError": {"title": "Bearbeitung fehlgeschlagen"}, "troubleMessage": "cubent hat Probleme...", "apiRequest": {"title": "API-Anfrage", "failed": "API-Anfrage fehlgeschlagen", "streaming": "API-Anfrage...", "cancelled": "API-Anfrage abgebrochen", "streamingFailed": "API-Streaming fehlgeschlagen"}, "checkpoint": {"initial": "Initialer Checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wird noch initialisiert... <PERSON> dies zu lange dauert, kannst du Checkpoints in den <settingsLink>Einstellungen</settingsLink> deaktivieren und deine Aufgabe neu starten.", "menu": {"viewDiff": "Unterschiede anzeigen", "restore": "Checkpoint wiederherstellen", "restoreFiles": "<PERSON><PERSON> wied<PERSON>", "restoreFilesDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde.", "restoreFilesAndTask": "Dateien & Aufgabe wiederherstellen", "confirm": "Bestätigen", "cancel": "Abbrechen", "cannotUndo": "Diese Aktion kann nicht rückgängig gemacht werden.", "restoreFilesAndTaskDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde, und löscht alle Nachrichten nach diesem Punkt."}, "current": "Aktuell"}, "instructions": {"wantsToFetch": "cubent möchte detaillierte Anweisungen abrufen, um bei der aktuellen Aufgabe zu helfen"}, "fileOperations": {"wantsToRead": "cubent möchte diese Datei lesen:", "wantsToReadAndXMore": "cubent möchte diese Datei und {{count}} weitere lesen:", "wantsToReadOutsideWorkspace": "cubent möchte diese Datei außerhalb des Arbeitsbereichs lesen:", "didRead": "cubent hat diese <PERSON>i gelesen:", "wantsToEdit": "cubent möchte diese Datei bearbeiten:", "wantsToEditOutsideWorkspace": "cubent möchte diese Datei außerhalb des Arbeitsbereichs bearbeiten:", "wantsToCreate": "cubent möchte eine neue Datei erstellen:", "wantsToSearchReplace": "cubent möchte in dieser Datei suchen und ersetzen:", "didSearchReplace": "cubent hat Suchen und Ersetzen in dieser Datei durchgeführt:", "wantsToInsert": "cubent möchte Inhalte in diese Datei einfügen:", "wantsToInsertWithLineNumber": "cubent möchte Inhalte in diese Datei in Zeile {{lineNumber}} einfügen:", "wantsToInsertAtEnd": "cubent möchte Inhalte am Ende dieser Datei anhängen:", "wantsToReadMultiple": "cubent möchte mehrere <PERSON>ien lesen:"}, "directoryOperations": {"wantsToViewTopLevel": "cubent möchte die Dateien auf oberster Ebene in diesem Verzeichnis anzeigen:", "didViewTopLevel": "cubent hat die Dateien auf oberster Ebene in diesem Verzeichnis angezeigt:", "wantsToViewRecursive": "cubent möchte rekursiv alle Dateien in diesem Verzeichnis anzeigen:", "didViewRecursive": "cubent hat rekursiv alle Dateien in diesem Verzeichnis angezeigt:", "wantsToViewDefinitions": "cubent möchte Quellcode-Definitionsnamen in diesem Verzeichnis anzeigen:", "didViewDefinitions": "cubent hat Quellcode-Definitionsnamen in diesem Verzeichnis angezeigt:", "wantsToSearch": "cubent möchte dieses Verzeichnis nach <code>{{regex}}</code> durchsuchen:", "didSearch": "cubent hat dieses Verzeichnis nach <code>{{regex}}</code> durchsucht:"}, "commandOutput": "Befehlsausgabe", "response": "Antwort", "arguments": "Argumente", "mcp": {"wantsToUseTool": "cubent möchte ein Tool auf dem {{serverName}} MCP-Server verwenden:", "wantsToAccessResource": "cubent möchte auf eine Ressource auf dem {{serverName}} MCP-Server zugreifen:"}, "modes": {"wantsToSwitch": "cubent möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln", "wantsToSwitchWithReason": "cubent möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln, weil: {{reason}}", "didSwitch": "cubent hat zum <code>{{mode}}</code>-Modus gewechselt", "didSwitchWithReason": "cubent hat zum <code>{{mode}}</code>-<PERSON><PERSON> gewechselt, weil: {{reason}}"}, "subtasks": {"wantsToCreate": "cubent möchte eine neue Teilaufgabe im <code>{{mode}}</code>-Modus erstellen:", "wantsToFinish": "cubent möchte diese Teilaufgabe abschließen", "newTaskContent": "Teilaufgabenanweisungen", "completionContent": "Teilaufgabe abgeschlossen", "resultContent": "Teilaufgabenergebnisse", "defaultResult": "Bitte fahre mit der nächsten Aufgabe fort.", "completionInstructions": "Teilaufgabe abgeschlossen! Du kannst die Ergebnisse überprüfen und Korrekturen oder nächste Schritte vorschlagen. Wenn alles gut aussieht, bestätige, um das Ergebnis an die übergeordnete Aufgabe zurückzugeben."}, "questions": {"hasQuestion": "<PERSON>ten auf Klärung:"}, "taskCompleted": "Ergebnisse", "powershell": {"issues": "<PERSON><PERSON> sche<PERSON>, dass du Probleme mit Windows PowerShell hast, bitte sieh dir dies an"}, "autoApprove": {"title": "Automatische Genehmigung:", "none": "<PERSON><PERSON>", "description": "Automatische Genehmigung erlaubt cubent Code, Aktionen ohne Nachfrage auszuführen. Aktiviere dies nur für Aktionen, denen du vollständig vertraust. Detailliertere Konfiguration verfügbar in den <settingsLink>Einstellungen</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON> nach", "seconds": "{{count}}s"}, "contextCondense": {"title": "Kontext komprimiert", "condensing": "Kontext wird komprimiert...", "errorHeader": "Kontext konnte nicht komprimiert werden", "tokens": "Tokens"}, "followUpSuggest": {"copyToInput": "In Eingabefeld kopieren (oder Shift + Klick)"}, "announcement": {"title": "🎉 cubent Code {{version}} ver<PERSON><PERSON><PERSON><PERSON>t", "description": "cubent Code {{version}} bringt leistungsstarke neue Funktionen und Verbesserungen basierend auf deinem Feedback.", "whatsNew": "Was ist neu", "feature1": "<bold>Intelligente Kontext-Kondensierung standardmäßig aktiviert</bold>: Kontext-Kondensierung ist jetzt standardmäßig aktiviert mit konfigurierbaren Einstellungen für automatische Kondensierung", "feature2": "<bold><PERSON><PERSON> Kondensierungs-<PERSON><PERSON></bold>: <PERSON><PERSON><PERSON> im Task-Header ermöglicht es dir, Kontext-Kondensierung jederzeit manuell auszulösen", "feature3": "<bold>Erweiterte Kondensierungs-Einstellungen</bold>: Feinabstimmung wann und wie automatische Kondensierung über die <contextSettingsLink>Kontext-Einstellungen</contextSettingsLink> erfolgt", "hideButton": "Ankündigung ausblenden", "detailsDiscussLinks": "Erhalte mehr Details und diskutiere auf <discordLink>Discord</discordLink> und <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "cubent möchte den Browser verwenden:", "consoleLogs": "Konsolenprotokolle", "noNewLogs": "(<PERSON><PERSON>)", "screenshot": "Browser-Screenshot", "cursor": "<PERSON><PERSON><PERSON>", "navigation": {"step": "Schritt {{current}} von {{total}}", "previous": "Zurück", "next": "<PERSON><PERSON>"}, "sessionStarted": "Browser-Sitzung gestartet", "actions": {"title": "Browser-Aktion: ", "launch": "Browser starten auf {{url}}", "click": "Klicken ({{coordinate}})", "type": "Eingeben \"{{text}}\"", "scrollDown": "Nach unten scrollen", "scrollUp": "Nach oben scrollen", "close": "<PERSON><PERSON><PERSON> schließen"}}, "codeblock": {"tooltips": {"expand": "Code-Block erweitern", "collapse": "Code-Block reduzieren", "enable_wrap": "Zeilenumbruch aktivieren", "disable_wrap": "Zeilenumbruch deaktivieren", "copy_code": "Code kopieren"}}, "systemPromptWarning": "WARNUNG: Benutzerdefinierte Systemaufforderung aktiv. Dies kann die Funktionalität erheblich beeinträchtigen und zu unvorhersehbarem Verhalten führen.", "profileViolationWarning": "Das aktuelle Profil verstößt gegen die Einstellungen deiner Organisation", "shellIntegration": {"title": "Befehlsausführungswarnung", "description": "Dein Be<PERSON>hl wird ohne VSCode Terminal-Shell-Integration ausgeführt. Um diese Warnung zu unterdrücken, kannst du die Shell-Integration im Abschnitt <strong>Terminal</strong> der <settingsLink>cubent Code Einstellungen</settingsLink> deaktivieren oder die VSCode Terminal-Integration mit dem Link unten beheben.", "troubleshooting": "<PERSON><PERSON>e hier für die Shell-Integrationsdokumentation."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limit für automatisch genehmigte Anfragen erreicht", "description": "cubent hat das automatisch genehmigte Limit von {{count}} API-Anfrage(n) erreicht. Möchtest du den Zähler zurücksetzen und mit der Aufgabe fortfahren?", "button": "Zurücksetzen und fortfahren"}}, "codebaseSearch": {"wantsToSearch": "cubent möchte den Codebase nach <code>{{query}}</code> durchsuchen:", "wantsToSearchWithPath": "cubent möchte den Codebase nach <code>{{query}}</code> in <code>{{path}}</code> durchsuchen:", "didSearch": "{{count}} <PERSON><PERSON><PERSON><PERSON>(se) für <code>{{query}}</code> gefunden:"}, "read-batch": {"approve": {"title": "Alle genehmigen"}, "deny": {"title": "<PERSON>e <PERSON>en"}}}