
> @cubent/types@0.0.0 build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\packages\types
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.5.0
[34mCLI[39m Using tsup config: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\packages\types\tsup.config.ts
[34mCLI[39m Target: es2022
[34mCJS[39m Build start
[34mESM[39m Build start
[32mESM[39m [1mdist\index.js     [22m[32m99.69 KB[39m
[32mESM[39m [1mdist\index.js.map [22m[32m185.04 KB[39m
[32mESM[39m ⚡️ Build success in 94ms
[32mCJS[39m [1mdist\index.cjs     [22m[32m113.10 KB[39m
[32mCJS[39m [1mdist\index.cjs.map [22m[32m185.92 KB[39m
[32mCJS[39m ⚡️ Build success in 100ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 4460ms
[32mDTS[39m [1mdist\index.d.cts [22m[32m585.71 KB[39m
[32mDTS[39m [1mdist\index.d.ts  [22m[32m585.71 KB[39m
