# Cubent Autocomplete Integration

This module integrates Continue.dev's autocomplete functionality into the Cubent VSCode extension, providing AI-powered inline code completion.

## Features

- **Multiple Model Support**: Choose from three recommended autocomplete models
- **Conflict Detection**: Automatically detects and avoids conflicts with GitHub Copilot
- **Performance Optimized**: Debouncing, caching, and efficient token management
- **Privacy Options**: Local model support via Ollama
- **Seamless Integration**: Works with existing Cubent architecture and settings

## Supported Models

### 1. Codestral (Mistral AI) - Best Performance
- **Provider**: Mistral AI
- **Model**: `codestral-latest`
- **Requires**: Mistral API key
- **Best for**: High-quality code completions
- **Get API Key**: [Mistral Console](https://console.mistral.ai/)

### 2. Mercury Coder Small (Inception Labs) - Best Speed/Quality
- **Provider**: Inception Labs
- **Model**: `mercury-coder-small`
- **Requires**: Inception Labs API key
- **Best for**: Fast, balanced completions
- **Get API Key**: [Inception Labs](https://inceptionlabs.ai/)

### 3. Qwen 2.5 Coder 1.5B (Ollama) - Local/Privacy
- **Provider**: Ollama (Local)
- **Model**: `qwen2.5-coder:1.5b`
- **Requires**: Ollama installation
- **Best for**: Privacy-first, offline completions
- **Setup**: [Ollama Installation Guide](https://ollama.ai/library/qwen2.5-coder)

## Configuration

### VSCode Settings

All settings are available in VSCode settings under `cubent.autocomplete.*`:

```json
{
  "cubent.autocomplete.enabled": false,
  "cubent.autocomplete.model": "codestral",
  "cubent.autocomplete.mistralApiKey": "",
  "cubent.autocomplete.inceptionApiKey": "",
  "cubent.autocomplete.ollamaBaseUrl": "http://localhost:11434",
  "cubent.autocomplete.allowWithCopilot": false,
  "cubent.autocomplete.debounceDelay": 300,
  "cubent.autocomplete.maxTokens": 256
}
```

### Settings UI

Access autocomplete settings through:
1. Open Cubent settings (Cmd/Ctrl + Shift + P → "Cubent: Open Settings")
2. Navigate to "Autocomplete" section
3. Configure your preferred model and API keys
4. Test connections using the "Test" buttons

## Architecture

### Core Components

- **`CubentAutocompleteProvider`**: Main VSCode inline completion provider
- **`IAutocompleteProvider`**: Interface for model-specific providers
- **Provider Implementations**:
  - `MistralAutocompleteProvider`
  - `InceptionLabsProvider`
  - `OllamaAutocompleteProvider`

### Integration Points

- **Extension Registration**: Registered in `extension.ts` when enabled
- **Settings UI**: Integrated into webview settings
- **Message Handling**: Configuration and testing via webview messages
- **Telemetry**: Usage tracking through existing telemetry service

## Usage

### Getting Started

1. **Enable Autocomplete**:
   - Open Cubent settings
   - Go to "Autocomplete" section
   - Toggle "Enable Autocomplete" to ON

2. **Choose a Model**:
   - Select from Codestral, Mercury Coder, or Qwen Coder
   - Each has different performance/privacy tradeoffs

3. **Configure API Keys** (for hosted models):
   - Enter your API key for the chosen provider
   - Use "Test" button to verify connection

4. **Start Coding**:
   - Autocomplete will trigger automatically as you type
   - Suggestions appear as inline gray text
   - Press Tab to accept, Esc to dismiss

### Conflict Prevention

- **GitHub Copilot Detection**: Automatically disabled when Copilot is active
- **Override Option**: Enable "Allow with Copilot" to use both (may cause conflicts)
- **Smart Triggering**: Avoids triggering in comments and inappropriate contexts

## Development

### Adding New Providers

1. Implement `IAutocompleteProvider` interface
2. Add provider to `CubentAutocompleteProvider.initializeProviders()`
3. Add configuration options to `package.json`
4. Update settings UI with new provider option

### Testing

```bash
# Run autocomplete tests
npm test -- --testPathPattern=autocomplete

# Run integration tests
npm test -- --testPathPattern=integration
```

### Debugging

- Enable VSCode Developer Tools: Help → Toggle Developer Tools
- Check console for autocomplete logs
- Use "Test Connection" buttons in settings to verify API connectivity

## Performance Considerations

- **Debouncing**: 300ms default delay prevents excessive API calls
- **Token Limits**: Configurable max tokens (default: 256)
- **Context Truncation**: Automatic prefix/suffix truncation for large files
- **Caching**: Built-in response caching for repeated requests

## Privacy & Security

- **API Keys**: Stored securely in VSCode settings
- **Local Option**: Qwen Coder runs entirely offline via Ollama
- **No Data Collection**: Completions are not logged or stored
- **Telemetry**: Only usage metrics (model choice, success/failure rates)

## Troubleshooting

### Common Issues

1. **No Completions Appearing**:
   - Check if autocomplete is enabled in settings
   - Verify API key is configured correctly
   - Test connection using "Test" button
   - Check for GitHub Copilot conflicts

2. **Slow Completions**:
   - Increase debounce delay in settings
   - Reduce max tokens setting
   - Consider switching to Mercury Coder for speed

3. **API Errors**:
   - Verify API key is valid and has credits
   - Check network connectivity
   - Review VSCode Developer Console for error details

4. **Ollama Issues**:
   - Ensure Ollama is running: `ollama serve`
   - Install Qwen model: `ollama pull qwen2.5-coder:1.5b`
   - Check Ollama base URL in settings

### Support

For issues and feature requests:
- Check existing issues in the Cubent repository
- Create new issue with autocomplete logs and configuration
- Include VSCode version and operating system details

## Future Enhancements

- **Additional Models**: Support for more code completion models
- **Context Integration**: Better integration with Cubent's codebase indexing
- **Smart Caching**: More sophisticated caching strategies
- **Performance Metrics**: Detailed completion quality analytics
- **Custom Models**: Support for custom fine-tuned models
