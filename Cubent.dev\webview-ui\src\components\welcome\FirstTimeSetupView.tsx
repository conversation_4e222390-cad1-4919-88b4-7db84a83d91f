import { useCallback, useState, useEffect, useMemo } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"

import { useExtensionState } from "@src/context/ExtensionStateContext"
import { validateApiConfiguration } from "@src/utils/validate"
import { vscode } from "@src/utils/vscode"
import { checkExistKey } from "@shared/checkExistApiConfig"

import { ApiKeyManagerContent } from "../settings/ApiKeyManagerPopup"
import { Tab, TabContent } from "../common/Tab"

import QaptHero from "./QaptHero"

const FirstTimeSetupView = () => {
	const { apiConfiguration, hiddenProfiles, currentApiConfigName } = useExtensionState()
	const [isLoading, setIsLoading] = useState(false)
	const [currentApiConfig, setCurrentApiConfig] = useState<any>(apiConfiguration)
	const [errorMessage, setErrorMessage] = useState<string | null>(null)
	const [retryCount, setRetryCount] = useState(0)

	// Sync local state with extension state when it changes
	useEffect(() => {
		setCurrentApiConfig(apiConfiguration)
	}, [apiConfiguration])

	// Listen for setup completion confirmation
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			if (message.type === "firstTimeSetupCompleted") {
				setIsLoading(false)
				if (message.success) {
					console.log("First-time setup completed successfully")
					setErrorMessage(null)
					setRetryCount(0)
				} else {
					console.error("First-time setup failed:", message.error)
					setErrorMessage(`Setup failed: ${message.error || "Unknown error"}`)
					setRetryCount(prev => prev + 1)
				}
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [])

	const handleContinue = useCallback(async () => {
		console.log("Continue button clicked, attempt:", retryCount + 1)
		setIsLoading(true)
		setErrorMessage(null)

		try {
			// First, save the current API configuration if it exists
			if (currentApiConfig) {
				console.log("Saving API configuration before completing setup:", currentApiConfig)

				// Validate the configuration before saving
				const error = validateApiConfiguration(currentApiConfig)
				if (error) {
					console.error("API configuration validation error:", error)
					setErrorMessage(`Configuration error: ${error}`)
					setIsLoading(false)
					return
				}

				// Save the API configuration and wait for confirmation
				console.log("Sending upsertApiConfiguration message")
				vscode.postMessage({
					type: "upsertApiConfiguration",
					text: currentApiConfigName || "default",
					apiConfiguration: currentApiConfig,
				})

				// Wait longer for the save to complete and state to sync
				console.log("Waiting for API configuration to be saved...")
				await new Promise((resolve) => setTimeout(resolve, 1000))
			} else {
				console.log("No API configuration to save, proceeding with setup completion")
			}

			// Then complete the first-time setup
			console.log("Sending completeFirstTimeSetup message")
			const message = { type: "completeFirstTimeSetup" as const }
			vscode.postMessage(message)
			console.log("Setup completion message sent - waiting for confirmation...")

			// Don't reset loading state here - wait for the confirmation message
			// The message handler will reset the loading state when it receives the response
		} catch (error) {
			console.error("Failed to complete setup:", error)
			const errorMsg = error instanceof Error ? error.message : "Unknown error occurred"
			setErrorMessage(`Setup failed: ${errorMsg}`)
			setRetryCount(prev => prev + 1)
			setIsLoading(false)
		}
	}, [currentApiConfig, currentApiConfigName, retryCount])

	const handleSkipSetup = useCallback(() => {
		console.log("Skip setup button clicked")
		// Send message to extension to skip setup for now
		console.log("Sending skipFirstTimeSetup message", vscode)
		console.log("vscode object:", vscode)
		const message = { type: "skipFirstTimeSetup" as const }
		console.log("Message to send:", message)
		vscode.postMessage(message)
		console.log("Message sent successfully")
	}, [])

	const handleApiConfigurationChange = useCallback(
		(config: any) => {
			console.log("API configuration changed:", config)
			// Update local state to track current API configuration
			setCurrentApiConfig(config)

			// Don't save immediately during setup - let the continue button handle it
			// This prevents race conditions and conflicting saves
			console.log("API configuration updated in local state")
		},
		[],
	)

	const handleHiddenProfilesChange = useCallback((profiles: string[]) => {
		// Handle hidden profiles changes - forward to extension
		vscode.postMessage({
			type: "setHiddenProfiles",
			profiles: profiles,
		})
	}, [])

	// Check if user has configured a valid API key using the same logic as the rest of the system
	const hasApiKey = useMemo(() => {
		if (!currentApiConfig) {
			return false
		}

		// Use the same validation logic as checkExistKey
		const hasValidConfig = checkExistKey(currentApiConfig)

		// Also check that validation passes (no errors)
		const validationError = validateApiConfiguration(currentApiConfig)
		const isValid = !validationError

		console.log("API key validation:", {
			hasValidConfig,
			isValid,
			validationError,
			currentApiConfig
		})

		return hasValidConfig && isValid
	}, [currentApiConfig])

	return (
		<Tab>
			<TabContent className="flex flex-col gap-5">
				<QaptHero />
				<div className="text-center -mt-4">
					<h2 className="text-xl font-bold mb-2">Let's get you started</h2>
					<p className="text-vscode-descriptionForeground">
						Let's get you set up with an AI model to start coding with Cubent.
					</p>
				</div>

				{/* API Key Manager without header and border */}
				<div>
					<ApiKeyManagerContent
						apiConfiguration={currentApiConfig}
						onApiConfigurationChange={handleApiConfigurationChange}
						hiddenProfiles={hiddenProfiles}
						onHiddenProfilesChange={handleHiddenProfilesChange}
						hiddenTabs={["builtin-models"]}
						centerTabs={true}
						hiddenProviders={["deepseek"]}
					/>
				</div>

				{/* Separator line */}
				<div className="w-full border-t border-vscode-foreground opacity-30 my-4"></div>

				<div className="p-4">
					<h4 className="font-semibold mb-2">What's Next?</h4>
					<ul className="text-sm text-vscode-descriptionForeground space-y-1">
						<li>• Add your API key for your preferred AI model</li>
						<li>• Start a conversation in the chat tab</li>
						<li>• Ask Cubent to help you write, debug, or explain code</li>
						<li>• Use @files to include specific files in your conversation</li>
					</ul>
				</div>
			</TabContent>

			<div className="sticky bottom-0 bg-vscode-sideBar-background p-3 border-t border-vscode-input-border">
				<div className="flex flex-col gap-2">
					{/* Error message display */}
					{errorMessage && (
						<div className="bg-red-900/20 border border-red-500/30 rounded p-2 text-xs text-red-300">
							<div className="flex items-center gap-2">
								<span>⚠️</span>
								<span>{errorMessage}</span>
							</div>
							{retryCount > 0 && (
								<div className="mt-1 text-xs text-red-400">
									Attempt {retryCount + 1} - Try again or check your configuration
								</div>
							)}
						</div>
					)}

					<div className="flex justify-center">
						<VSCodeButton
							onClick={handleContinue}
							appearance="primary"
							disabled={isLoading}
							style={{
								width: "150px",
								borderRadius: "8px",
							}}>
							{isLoading ? "Setting up..." : retryCount > 0 ? "Retry" : "Continue"}
						</VSCodeButton>
					</div>

					<button
						onClick={handleSkipSetup}
						className="text-xs text-vscode-descriptionForeground hover:text-vscode-foreground cursor-pointer bg-transparent border-none p-1">
						I'll set up an API key later
					</button>

					{!hasApiKey && !errorMessage && (
						<p className="text-xs text-vscode-descriptionForeground text-center">
							Add an API key above to start using Cubent immediately
						</p>
					)}
				</div>
			</div>
		</Tab>
	)
}

export default FirstTimeSetupView
