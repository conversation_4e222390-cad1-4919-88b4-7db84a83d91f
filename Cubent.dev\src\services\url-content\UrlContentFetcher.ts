import { <PERSON><PERSON><PERSON> } from "jsdom"
import { Readability } from "@mozilla/readability"
import { NodeHtmlMarkdown } from "node-html-markdown"

export interface UrlContentResult {
	success: boolean
	content?: string
	title?: string
	url: string
	error?: string
}

export class UrlContentFetcher {
	private static readonly MAX_CONTENT_LENGTH = 20000
	private static readonly REQUEST_TIMEOUT = 10000 // 10 seconds
	private static readonly USER_AGENT =
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

	/**
	 * Fetches and processes content from a URL
	 */
	static async fetchUrlContent(url: string): Promise<UrlContentResult> {
		try {
			// Validate URL
			const parsedUrl = new URL(url)
			if (!["http:", "https:"].includes(parsedUrl.protocol)) {
				return {
					success: false,
					url,
					error: "Only HTTP and HTTPS URLs are supported",
				}
			}

			// Fetch the content
			const controller = new AbortController()
			const timeoutId = setTimeout(() => controller.abort(), this.REQUEST_TIMEOUT)

			const response = await fetch(url, {
				headers: {
					"User-Agent": this.USER_AGENT,
					Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
					"Accept-Language": "en-US,en;q=0.5",
					"Accept-Encoding": "gzip, deflate, br",
					DNT: "1",
					Connection: "keep-alive",
					"Upgrade-Insecure-Requests": "1",
				},
				signal: controller.signal,
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				return {
					success: false,
					url,
					error: `HTTP ${response.status}: ${response.statusText}`,
				}
			}

			// Check content type
			const contentType = response.headers.get("content-type") || ""
			if (!contentType.includes("text/html")) {
				return {
					success: false,
					url,
					error: `Unsupported content type: ${contentType}. Only HTML content is supported.`,
				}
			}

			const html = await response.text()

			// Process with Readability
			const result = this.processHtmlContent(html, url)

			return {
				success: true,
				url,
				...result,
			}
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					return {
						success: false,
						url,
						error: "Request timed out",
					}
				}
				return {
					success: false,
					url,
					error: error.message,
				}
			}
			return {
				success: false,
				url,
				error: "Unknown error occurred",
			}
		}
	}

	/**
	 * Processes HTML content using Readability and converts to markdown
	 */
	private static processHtmlContent(html: string, url: string): { content: string; title?: string } {
		try {
			// Create DOM
			const dom = new JSDOM(html, { url })
			const document = dom.window.document

			// Extract title
			const title = document.title || undefined

			// Use Readability to extract main content
			const reader = new Readability(document)
			const article = reader.parse()

			if (!article) {
				// Fallback: try to extract content from body
				const bodyContent = document.body?.textContent || ""
				return {
					content: this.truncateContent(bodyContent.trim()),
					title,
				}
			}

			// Convert HTML to markdown
			const markdown = NodeHtmlMarkdown.translate(article.content || "", {
				useInlineLinks: true,
				useLinkReferenceDefinitions: false,
				keepDataImages: false,
				maxConsecutiveNewlines: 2,
			})

			return {
				content: this.truncateContent(markdown.trim()),
				title: article.title || title,
			}
		} catch (error) {
			console.error("Error processing HTML content:", error)
			// Fallback to raw text extraction
			try {
				const dom = new JSDOM(html)
				const textContent = dom.window.document.body?.textContent || ""
				return {
					content: this.truncateContent(textContent.trim()),
					title: dom.window.document.title || undefined,
				}
			} catch {
				return {
					content: "Failed to extract content from the webpage",
					title: undefined,
				}
			}
		}
	}

	/**
	 * Truncates content to prevent overwhelming responses
	 */
	private static truncateContent(content: string): string {
		if (content.length <= this.MAX_CONTENT_LENGTH) {
			return content
		}

		const truncated = content.substring(0, this.MAX_CONTENT_LENGTH)
		const lastNewline = truncated.lastIndexOf("\n")

		// Try to cut at a natural break point
		if (lastNewline > this.MAX_CONTENT_LENGTH * 0.8) {
			return truncated.substring(0, lastNewline) + "\n\n[Content truncated - original content was longer]"
		}

		return truncated + "\n\n[Content truncated - original content was longer]"
	}

	/**
	 * Validates if a URL is accessible
	 */
	static async validateUrl(url: string): Promise<{ valid: boolean; error?: string }> {
		try {
			new URL(url)
			return { valid: true }
		} catch {
			return { valid: false, error: "Invalid URL format" }
		}
	}
}
