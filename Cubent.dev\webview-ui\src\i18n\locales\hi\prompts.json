{"title": "मोड्स", "done": "हो गया", "modes": {"title": "मोड्स", "createNewMode": "नया मोड बनाएँ", "editModesConfig": "मोड कॉन्फ़िगरेशन संपादित करें", "editGlobalModes": "ग्लोबल मोड्स संपादित करें", "editProjectModes": "प्रोजेक्ट मोड्स संपादित करें (.roomodes)", "createModeHelpText": "मोड विशेष व्यक्तित्व हैं जो cubent के व्यवहार को अनुकूलित करते हैं। <0>मोड का उपयोग करने के बारे में जानें</0> या <1>मोड को अनुकूलित करना।</1>", "selectMode": "मोड खोजें"}, "apiConfiguration": {"title": "API कॉन्फ़िगरेशन", "select": "इस मोड के लिए किस API कॉन्फ़िगरेशन का उपयोग करना है, चुनें"}, "tools": {"title": "उपलब्ध टूल्स", "builtInModesText": "अंतर्निहित मोड्स के लिए टूल्स को संशोधित नहीं किया जा सकता", "editTools": "टूल्स संपादित करें", "doneEditing": "संपादन पूरा हुआ", "allowedFiles": "अनुमत फाइलें:", "toolNames": {"read": "फाइलें पढ़ें", "edit": "फाइलें संपादित करें", "browser": "ब्राउज़र का उपयोग करें", "command": "कमांड्स चलाएँ", "mcp": "MCP का उपयोग करें"}, "noTools": "कोई नहीं"}, "roleDefinition": {"title": "भूमिका परिभाषा", "resetToDefault": "डिफ़ॉल्ट पर रीसेट करें", "description": "इस मोड के लिए cubent की विशेषज्ञता और व्यक्तित्व परिभाषित करें। यह विवरण cubent के स्वयं को प्रस्तुत करने और कार्यों से निपटने के तरीके को आकार देता है।"}, "whenToUse": {"title": "कब उपयोग करें (वैकल्पिक)", "description": "बताएं कि इस मोड का उपयोग कब किया जाना चाहिए। यह Orchestrator को किसी कार्य के लिए सही मोड चुनने में मदद करता है।", "resetToDefault": "'कब उपयोग करें' विवरण को डिफ़ॉल्ट पर रीसेट करें"}, "customInstructions": {"title": "मोड-विशिष्ट कस्टम निर्देश (वैकल्पिक)", "resetToDefault": "डिफ़ॉल्ट पर रीसेट करें", "description": "{{modeName}} मोड के लिए विशिष्ट व्यवहार दिशानिर्देश जोड़ें।", "loadFromFile": "{{mode}} मोड के लिए विशिष्ट कस्टम निर्देश आपके वर्कस्पेस में <span>.cubent/rules-{{slug}}/</span> फ़ोल्डर से भी लोड किए जा सकते हैं (.roorules-{{slug}} और .clinerules-{{slug}} पुराने हो गए हैं और जल्द ही काम करना बंद कर देंगे)।"}, "globalCustomInstructions": {"title": "सभी मोड्स के लिए कस्टम निर्देश", "description": "ये निर्देश सभी मोड्स पर लागू होते हैं। वे व्यवहारों का एक आधार सेट प्रदान करते हैं जिन्हें नीचे दिए गए मोड-विशिष्ट निर्देशों द्वारा बढ़ाया जा सकता है। <0>और जानें</0>", "loadFromFile": "निर्देश आपके वर्कस्पेस में <span>.cubent/rules/</span> फ़ोल्डर से भी लोड किए जा सकते हैं (.roorules और .clinerules पुराने हो गए हैं और जल्द ही काम करना बंद कर देंगे)।"}, "systemPrompt": {"preview": "सिस्टम प्रॉम्प्ट का पूर्वावलोकन", "copy": "सिस्टम प्रॉम्प्ट को क्लिपबोर्ड पर कॉपी करें", "title": "सिस्टम प्रॉम्प्ट ({{modeName}} मोड)"}, "supportPrompts": {"title": "सहायता प्रॉम्प्ट्स", "resetPrompt": "{{promptType}} प्रॉम्प्ट को डिफ़ॉल्ट पर रीसेट करें", "prompt": "प्रॉम्प्ट", "enhance": {"apiConfiguration": "API कॉन्फ़िगरेशन", "apiConfigDescription": "आप प्रॉम्प्ट्स को बढ़ाने के लिए हमेशा उपयोग करने के लिए एक API कॉन्फ़िगरेशन चुन सकते हैं, या बस वर्तमान में चयनित का उपयोग कर सकते हैं", "useCurrentConfig": "वर्तमान में चयनित API कॉन्फ़िगरेशन का उपयोग करें", "testPromptPlaceholder": "वृद्धि का परीक्षण करने के लिए एक प्रॉम्प्ट दर्ज करें", "previewButton": "प्रॉम्प्ट वृद्धि का पूर्वावलोकन", "testEnhancement": "वृद्धि का परीक्षण करें"}, "types": {"ENHANCE": {"label": "प्रॉम्प्ट बढ़ाएँ", "description": "अपने इनपुट के लिए अनुकूलित सुझाव या सुधार प्राप्त करने के लिए प्रॉम्प्ट वृद्धि का उपयोग करें। यह सुनिश्चित करता है कि cubent आपके इरादे को समझता है और सर्वोत्तम संभव प्रतिक्रियाएँ प्रदान करता है। चैट में ✨ आइकन के माध्यम से उपलब्ध है।"}, "EXPLAIN": {"label": "कोड समझाएँ", "description": "कोड स्निपेट, फंक्शन या पूरी फाइलों के विस्तृत स्पष्टीकरण प्राप्त करें। जटिल कोड को समझने या नए पैटर्न सीखने के लिए उपयोगी। कोड कार्रवाइयों (एडिटर में बल्ब आइकन) और एडिटर के कंटेक्स्ट मेनू (चयनित कोड पर राइट-क्लिक) में उपलब्ध है।"}, "FIX": {"label": "समस्याएँ ठीक करें", "description": "बग्स, त्रुटियों या कोड गुणवत्ता के मुद्दों की पहचान और समाधान में मदद प्राप्त करें। समस्याओं को ठीक करने के लिए चरण-दर-चरण मार्गदर्शन प्रदान करता है। कोड कार्रवाइयों (एडिटर में बल्ब आइकन) और एडिटर के कंटेक्स्ट मेनू (चयनित कोड पर राइट-क्लिक) में उपलब्ध है।"}, "IMPROVE": {"label": "कोड में सुधार करें", "description": "कार्यक्षमता बनाए रखते हुए कोड अनुकूलन, बेहतर प्रथाओं और वास्तुकला सुधारों के लिए सुझाव प्राप्त करें। कोड कार्रवाइयों (एडिटर में बल्ब आइकन) और एडिटर के कंटेक्स्ट मेनू (चयनित कोड पर राइट-क्लिक) में उपलब्ध है।"}, "ADD_TO_CONTEXT": {"label": "संदर्भ में जोड़ें", "description": "अपने वर्तमान कार्य या वार्तालाप में संदर्भ जोड़ें। अतिरिक्त जानकारी या स्पष्टीकरण प्रदान करने के लिए उपयोगी। कोड कार्रवाइयों (एडिटर में बल्ब आइकन) और एडिटर के कंटेक्स्ट मेनू (चयनित कोड पर राइट-क्लिक) में उपलब्ध है।"}, "TERMINAL_ADD_TO_CONTEXT": {"label": "टर्मिनल सामग्री को संदर्भ में जोड़ें", "description": "अपने वर्तमान कार्य या वार्तालाप में टर्मिनल आउटपुट जोड़ें। कमांड आउटपुट या लॉग प्रदान करने के लिए उपयोगी। टर्मिनल के कंटेक्स्ट मेनू (चयनित टर्मिनल सामग्री पर राइट-क्लिक) में उपलब्ध है।"}, "TERMINAL_FIX": {"label": "टर्मिनल कमांड ठीक करें", "description": "विफल हुए या सुधार की आवश्यकता वाले टर्मिनल कमांड को ठीक करने में मदद प्राप्त करें। टर्मिनल के कंटेक्स्ट मेनू (चयनित टर्मिनल सामग्री पर राइट-क्लिक) में उपलब्ध है।"}, "TERMINAL_EXPLAIN": {"label": "टर्मिनल कमांड समझाएँ", "description": "टर्मिनल कमांड और उनके आउटपुट के विस्तृत स्पष्टीकरण प्राप्त करें। टर्मिनल के कंटेक्स्ट मेनू (चयनित टर्मिनल सामग्री पर राइट-क्लिक) में उपलब्ध है।"}, "NEW_TASK": {"label": "नया कार्य शुरू करें", "description": "इनपुट के साथ नया कार्य शुरू करें। कमांड पैलेट में उपलब्ध है।"}}}, "advancedSystemPrompt": {"title": "उन्नत: सिस्टम प्रॉम्प्ट ओवरराइड करें", "description": "<2>⚠️ चेतावनी:</2> यह उन्नत सुविधा सुरक्षा उपायों को दरकिनार करती है। <1>उपयोग करने से पहले इसे पढ़ें!</1>अपने वर्कस्पेस में <span>.cubent/system-prompt-{{slug}}</span> पर एक फ़ाइल बनाकर डिफ़ॉल्ट सिस्टम प्रॉम्प्ट को ओवरराइड करें।"}, "createModeDialog": {"title": "नया मोड बनाएँ", "close": "ब<PERSON><PERSON> करें", "name": {"label": "नाम", "placeholder": "मोड का नाम दर्ज करें"}, "slug": {"label": "स्लग", "description": "स्लग URL और फाइल नामों में उपयोग किया जाता है। यह लोअरकेस में होना चाहिए और केवल अक्षर, संख्याएँ और हाइफन शामिल होने चाहिए।"}, "saveLocation": {"label": "सहेजने का स्थान", "description": "इस मोड को कहां सहेजना है, चुनें। प्रोजेक्ट-विशिष्ट मोड्स को ग्लोबल मोड्स पर प्राथमिकता मिलती है।", "global": {"label": "ग्लोबल", "description": "सभी वर्कस्पेस में उपलब्ध"}, "project": {"label": "प्रोजेक्ट-विशिष्ट (.roomodes)", "description": "केवल इस वर्कस्पेस में उपलब्ध, ग्लोबल पर प्राथमिकता रखता है"}}, "roleDefinition": {"label": "भूमिका परिभाषा", "description": "इस मोड के लिए cubent की विशेषज्ञता और व्यक्तित्व परिभाषित करें।"}, "whenToUse": {"label": "कब उपयोग करें (वैकल्पिक)", "description": "स्पष्ट रूप से बताएं कि यह मोड कब सबसे प्रभावी है और किस प्रकार के कार्यों में यह उत्कृष्ट है।"}, "tools": {"label": "उपलब्ध टूल्स", "description": "चुनें कि यह मोड कौन से टूल्स उपयोग कर सकता है।"}, "customInstructions": {"label": "कस्टम निर्देश (वैकल्पिक)", "description": "इस मोड के लिए विशिष्ट व्यवहार दिशानिर्देश जोड़ें।"}, "buttons": {"cancel": "रद्<PERSON> करें", "create": "मोड बनाएँ"}, "deleteMode": "मोड हटाएँ"}, "allFiles": "सभी फाइलें"}